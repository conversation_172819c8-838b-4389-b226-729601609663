#!/usr/bin/env python3
"""
Gradio UI application for marine equipment data extraction.

This module provides a modern Gradio interface with configuration management,
improved user experience, and comprehensive extraction capabilities.
"""

import gradio as gr
import pandas as pd
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime

from ..config.settings import settings
from ..config.config_manager import config_manager, ConfigManager
from ..core.base_classes import ExtractionType
from ..retrieval.retrieval_service import RetrievalService
from ..extraction.extraction_service import ExtractionService, ExtractionMethod
from ..core.utils import setup_logging

# Import PDF processing function
try:
    from ingest_pdf import process_uploaded_pdf
except ImportError:
    def process_uploaded_pdf(pdf_file_path: str, progress_callback=None):
        return {"success": False, "message": "PDF processing not available", "stats": {}}

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


class ExtractionApp:
    """Main application class for the Gradio interface."""
    
    def __init__(self):
        """Initialize the application."""
        self.retrieval_service = None
        self.extractor = None
        self.current_chunks = []
        self.current_results = []
        self.config_manager = ConfigManager()
        self.last_processed_file = None

        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize application components."""
        try:
            # Initialize retrieval service
            self.retrieval_service = RetrievalService()
            logger.info("Retrieval service initialized")

            # Test connection
            if not self.retrieval_service.retriever.health_check():
                logger.error("Supabase connection failed")

        except Exception as e:
            logger.error(f"Failed to initialize components: {e}")
    
    def get_available_files(self) -> List[str]:
        """Get available files from database."""
        try:
            if self.retrieval_service:
                files = self.retrieval_service.retriever.get_available_files()
                logger.info(f"Retrieved {len(files)} files from database: {files}")
                if not files:
                    logger.warning("No files found in database - check if PDFs have been uploaded")
                    return ["No files available - Please upload PDFs first"]
                return files
            else:
                logger.error("Retrieval service not initialized")
                return ["Database connection error - Check configuration"]
        except Exception as e:
            logger.error(f"Failed to get available files: {e}")
            return [f"Error loading files: {str(e)}"]
    
    def get_available_models(self) -> Dict[str, List[str]]:
        """Get available models by provider."""
        return settings.get_available_models()
    
    def get_extraction_types(self) -> List[str]:
        """Get available extraction types."""
        return [et.value for et in ExtractionType]

    def test_database_connection(self) -> str:
        """Test database connection and return status."""
        try:
            if not self.retrieval_service:
                return "❌ Retrieval service not initialized"

            # Test connection
            health_check = self.retrieval_service.retriever.health_check()
            if health_check:
                files = self.retrieval_service.retriever.get_available_files()
                return f"✅ Database connected. Found {len(files)} files."
            else:
                return "❌ Database connection failed"

        except Exception as e:
            return f"❌ Database test failed: {str(e)}"

    def get_config_for_editing(self, extraction_type: str) -> Tuple[str, str, str]:
        """
        Get configuration components for editing.

        Args:
            extraction_type: Type of extraction

        Returns:
            Tuple of (schema_json, system_prompt, search_queries_text)
        """
        try:
            extraction_enum = ExtractionType(extraction_type)
            config = config_manager.get_extraction_config(extraction_enum)

            # Format schema as JSON
            schema_json = json.dumps(config.schema, indent=2)

            # Format search queries as text (one per line)
            search_queries_text = '\n'.join(config.search_queries)

            return schema_json, config.system_prompt, search_queries_text

        except Exception as e:
            logger.error(f"Failed to get config for editing: {e}")
            return "{}", "", ""

    def save_config_changes(self, extraction_type: str, schema_json: str,
                          system_prompt: str, search_queries_text: str) -> str:
        """
        Save configuration changes.

        Args:
            extraction_type: Type of extraction
            schema_json: Schema as JSON string
            system_prompt: System prompt text
            search_queries_text: Search queries (one per line)

        Returns:
            Status message
        """
        try:
            extraction_enum = ExtractionType(extraction_type)

            # Step 1: Validate JSON schema and test Pydantic model generation
            logger.info(f"🔧 [CONFIG EDITOR] Validating schema for {extraction_type}")
            validation_result = self._validate_and_test_schema(extraction_type, schema_json)
            if not validation_result["valid"]:
                return f"❌ Schema validation failed: {validation_result['error']}"

            logger.info(f"✅ [CONFIG EDITOR] Schema validation passed for {extraction_type}")

            # Parse validated schema and search queries
            schema = json.loads(schema_json)
            search_queries = [line.strip() for line in search_queries_text.split('\n')
                            if line.strip()]

            # Save to files using the global config manager
            from ..config.config_manager import config_manager

            # Save schema
            schema_file = config_manager.schemas_dir / f"{extraction_type}.json"
            with open(schema_file, 'w', encoding='utf-8') as f:
                json.dump(schema, f, indent=2)

            # Save system prompt
            prompt_file = config_manager.prompts_dir / f"{extraction_type}.txt"
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(system_prompt)

            # Save search queries
            queries_file = config_manager.queries_dir / f"{extraction_type}.txt"
            with open(queries_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(search_queries))

            # Step 2: Regenerate Pydantic models from all schemas
            logger.info(f"🔧 [CONFIG EDITOR] Regenerating Pydantic models")
            regeneration_result = self._regenerate_pydantic_models()

            if not regeneration_result["success"]:
                # Configuration was saved but model regeneration failed
                logger.error(f"Model regeneration failed: {regeneration_result['error']}")
                return f"⚠️ Configuration saved but model regeneration failed: {regeneration_result['error']}"

            logger.info(f"✅ [CONFIG EDITOR] Pydantic models regenerated successfully")

            # Step 3: Reload the specific configuration to force refresh
            config_manager.reload_config(extraction_enum)

            logger.info(f"Configuration reloaded for {extraction_type}")

            # Success message with details
            success_msg = f"✅ Configuration saved successfully for {extraction_type}"
            if regeneration_result.get("backup_created"):
                success_msg += "\n📦 Backup of previous models created"
            success_msg += "\n🔄 Pydantic models regenerated and synchronized"

            return success_msg

        except Exception as e:
            logger.error(f"Failed to save config changes: {e}")
            return f"❌ Failed to save configuration: {e}"
    
    def perform_extraction(self, file_name: str, extraction_type: str, model_name: str,
                         use_search_queries: bool, max_chunks: int,
                         extraction_method: str = "auto") -> Tuple[str, str, str]:
        """
        Perform data extraction.
        
        Returns:
            Tuple of (status_message, results_json, results_csv)
        """
        try:
            # Debug logging
            logger.info(f"perform_extraction called with: file_name='{file_name}', extraction_type='{extraction_type}', model_name='{model_name}'")

            if not file_name:
                logger.warning("No file_name provided to perform_extraction")
                return "Please select a file", "", ""

            # Check for error messages in file_name (from get_available_files)
            if file_name.startswith("No files available") or file_name.startswith("Database connection error") or file_name.startswith("Error loading files"):
                return f"Cannot extract: {file_name}", "", ""

            if not extraction_type:
                return "Please select an extraction type", "", ""

            if not model_name:
                return "Please select a model", "", ""
            
            # Initialize extraction service with selected model
            self.extraction_service = ExtractionService(model_name=model_name)
            
            # Get extraction configuration
            extraction_enum = ExtractionType(extraction_type)
            config = config_manager.get_extraction_config(extraction_enum)
            
            # Retrieve chunks using shared service
            self.current_chunks = self.retrieval_service.retrieve_chunks(
                file_name, config, max_chunks, use_search_queries
            )

            if not self.current_chunks:
                return f"No chunks found for file: {file_name}", "", ""

            # Log page numbers of processed chunks
            page_numbers = [chunk.page_number for chunk in self.current_chunks]
            status_msg = f"Processing {len(self.current_chunks)} chunks with {model_name} from pages: {sorted(set(page_numbers))}"
            logger.info(status_msg)
            
            # Determine extraction method
            if extraction_method == "auto":
                selected_method = self.extraction_service.get_recommended_method(self.current_chunks)
                logger.info(f"Auto-selected {selected_method.value} extraction method for UI")
            else:
                selected_method = ExtractionMethod(extraction_method)
                logger.info(f"Using specified {selected_method.value} extraction method for UI")

            # Perform extraction
            result = self.extraction_service.extract(
                self.current_chunks,
                config,
                method=selected_method
            )
            
            if not result.success:
                return f"Extraction failed: {result.error}", "", ""
            
            self.current_results = result.data or []
            
            # Generate outputs
            results_json = json.dumps(self.current_results, indent=2)
            
            # Create CSV
            if self.current_results:
                df = pd.DataFrame(self.current_results)
                results_csv = df.to_csv(index=False)
            else:
                results_csv = ""
            
            # Create status message
            token_info = ""
            if result.token_usage:
                token_info = f" (Tokens: {result.token_usage.total_tokens}, Cost: ${result.token_usage.estimated_cost_usd:.4f})"
            
            status_msg = f"✅ Extraction completed: {len(self.current_results)} items extracted{token_info}"
            
            return status_msg, results_json, results_csv
            
        except Exception as e:
            error_msg = f"Extraction error: {str(e)}"
            logger.error(error_msg)
            return error_msg, "", ""
    


    def download_extraction_json(self):
        """
        Generate JSON file for extraction results download.

        Returns:
            File path for download or None if no results available
        """
        if not self.current_results:
            logger.warning("No extraction results available for download")
            return None

        try:
            # Generate JSON file with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extraction_results_{timestamp}.json"

            # Create temporary file
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)

            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_results, f, indent=2, ensure_ascii=False)

            logger.info(f"Generated JSON download file: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"Error generating extraction JSON: {e}")
            return None

    def download_extraction_csv(self):
        """
        Generate CSV file for extraction results download.

        Returns:
            File path for download or None if no results available
        """
        if not self.current_results:
            logger.warning("No extraction results available for download")
            return None

        try:
            # Generate CSV file with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"extraction_results_{timestamp}.csv"

            # Create temporary file
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)

            # Convert to DataFrame and save as CSV
            df = pd.DataFrame(self.current_results)
            df.to_csv(temp_path, index=False)

            logger.info(f"Generated CSV download file: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"Error generating extraction CSV: {e}")
            return None

    def update_download_buttons(self):
        """Update download buttons with file paths after extraction."""
        if not self.current_results:
            return [gr.DownloadButton(visible=False), gr.DownloadButton(visible=False)]

        # Generate the files and return DownloadButtons with values
        json_path = self.download_extraction_json()
        csv_path = self.download_extraction_csv()

        return [
            gr.DownloadButton("📥 Download JSON", visible=True, value=json_path),
            gr.DownloadButton("📥 Download CSV", visible=True, value=csv_path)
        ]

    def get_chunk_preview(self) -> str:
        """Get preview of current chunks."""
        if not self.current_chunks:
            return "No chunks loaded"
        
        preview_parts = []
        for i, chunk in enumerate(self.current_chunks[:3]):  # Show first 3 chunks
            preview_parts.append(f"**Chunk {i+1} (Page {chunk.page_number})**")
            preview_parts.append(chunk.text_raw[:500] + "..." if len(chunk.text_raw) > 500 else chunk.text_raw)
            preview_parts.append("---")
        
        if len(self.current_chunks) > 3:
            preview_parts.append(f"... and {len(self.current_chunks) - 3} more chunks")
        
        return "\n\n".join(preview_parts)

    def get_page_numbers(self) -> str:
        """Get page numbers for display."""
        if not self.current_chunks:
            return ""
        page_numbers = sorted(set(chunk.page_number for chunk in self.current_chunks))
        return ', '.join(map(str, page_numbers))

    def _validate_and_test_schema(self, extraction_type: str, schema_json: str) -> Dict[str, Any]:
        """
        Validate JSON schema and test Pydantic model generation.

        Args:
            extraction_type: Type of extraction
            schema_json: JSON schema as string

        Returns:
            Dictionary with validation results
        """
        try:
            # Parse JSON
            schema = json.loads(schema_json)

            # Test Pydantic model generation
            from ..extraction.schema_migrator import SchemaMigrator
            from pathlib import Path
            import tempfile

            # Create temporary schema file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump(schema, temp_file, indent=2)
                temp_schema_path = Path(temp_file.name)

            try:
                # Test model generation
                migrator = SchemaMigrator(temp_schema_path.parent, Path("temp_models.py"))
                model_info = migrator.extract_model_info_from_schema(schema, extraction_type)
                model_code = migrator.generate_model_code(extraction_type, model_info)

                # Test if the generated code is valid Python
                compile(model_code, f"<{extraction_type}_model>", "exec")

                # Clean up
                temp_schema_path.unlink()

                return {
                    "valid": True,
                    "message": "Schema validation passed",
                    "model_info": model_info
                }

            except Exception as e:
                # Clean up
                if temp_schema_path.exists():
                    temp_schema_path.unlink()
                return {
                    "valid": False,
                    "error": f"Pydantic model generation failed: {str(e)}"
                }

        except json.JSONDecodeError as e:
            return {
                "valid": False,
                "error": f"Invalid JSON: {str(e)}"
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"Schema validation error: {str(e)}"
            }

    def _regenerate_pydantic_models(self) -> Dict[str, Any]:
        """
        Regenerate Pydantic models from all schemas.

        Returns:
            Dictionary with regeneration results
        """
        try:
            from ..extraction.schema_migrator import SchemaMigrator
            from ..config.config_manager import config_manager
            from pathlib import Path

            # Paths
            schemas_dir = config_manager.schemas_dir
            models_file = Path("src/extraction/models.py")
            backup_file = Path("src/extraction/models_backup.py")

            # Create migrator
            migrator = SchemaMigrator(schemas_dir, models_file)

            # Create backup of existing models
            if models_file.exists():
                import shutil
                shutil.copy2(models_file, backup_file)

            # Generate new models
            if migrator.migrate_schemas():
                return {
                    "success": True,
                    "message": "Pydantic models regenerated successfully",
                    "backup_created": backup_file.exists()
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to regenerate Pydantic models"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Model regeneration error: {str(e)}"
            }

    def process_pdf_upload(self, pdf_file):
        """
        Process uploaded PDF file.

        Args:
            pdf_file: Uploaded PDF file from Gradio

        Returns:
            Tuple of (status_message, progress_log, processing_stats, csv_btn_update, json_btn_update)
        """
        if pdf_file is None:
            return "❌ No file selected", "", {}, gr.update(visible=False), gr.update(visible=False)

        try:
            # Progress tracking
            progress_log = []

            def progress_callback(message: str):
                progress_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
                return "\n".join(progress_log)

            progress_callback("Starting PDF upload processing...")

            # Process the PDF
            result = process_uploaded_pdf(pdf_file.name, progress_callback)

            if result["success"]:
                status_msg = f"✅ Successfully processed {result['file_name']}"
                progress_callback(f"Processing completed successfully!")
                progress_callback(f"Pages processed: {result['stats'].get('total_pages', 0)}")
                progress_callback(f"Pages with images: {result['stats'].get('pages_with_images', 0)}")
                progress_callback(f"OCR API calls: {result['stats'].get('ocr_calls', 0)}")

                # Store the processed file name for downloads
                self.last_processed_file = Path(pdf_file.name).stem

                # Show download buttons
                return status_msg, "\n".join(progress_log), result["stats"], gr.update(visible=True), gr.update(visible=True)
            else:
                status_msg = f"❌ Failed to process PDF: {result['message']}"
                progress_callback(f"Processing failed: {result['message']}")
                return status_msg, "\n".join(progress_log), result["stats"], gr.update(visible=False), gr.update(visible=False)

        except Exception as e:
            error_msg = f"❌ Error processing PDF: {str(e)}"
            logger.error(f"PDF processing error: {e}")
            return error_msg, f"Error: {str(e)}", {}, gr.update(visible=False), gr.update(visible=False)

    def download_processed_csv(self):
        """
        Generate CSV file for the last processed PDF data.

        Returns:
            File path for download or None if no data available
        """
        if not self.last_processed_file:
            logger.warning("No processed file available for download")
            return None

        try:
            # Query the database for the processed data
            from ..retrieval.supabase_retriever import SupabaseRetriever
            retriever = SupabaseRetriever()

            # Get all chunks for the processed file
            response = retriever.supabase.table("pdf_documents").select("*").eq("file_name", f"{self.last_processed_file}.pdf").execute()

            if not response.data:
                logger.warning(f"No data found for processed file: {self.last_processed_file}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(response.data)

            # Generate filename
            filename = f"{self.last_processed_file}_processed_data.csv"

            # Create temporary file
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)

            df.to_csv(temp_path, index=False)

            logger.info(f"Generated processed CSV download file: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"Error generating processed CSV: {e}")
            return None

    def download_processed_json(self):
        """
        Generate JSON file for the last processed PDF data.

        Returns:
            File path for download or None if no data available
        """
        if not self.last_processed_file:
            logger.warning("No processed file available for download")
            return None

        try:
            # Query the database for the processed data
            from ..retrieval.supabase_retriever import SupabaseRetriever
            retriever = SupabaseRetriever()

            # Get all chunks for the processed file
            response = retriever.supabase.table("pdf_documents").select("*").eq("file_name", f"{self.last_processed_file}.pdf").execute()

            if not response.data:
                logger.warning(f"No data found for processed file: {self.last_processed_file}")
                return None

            # Generate filename
            filename = f"{self.last_processed_file}_processed_data.json"

            # Create temporary file
            import tempfile
            import os

            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, filename)

            with open(temp_path, 'w', encoding='utf-8') as f:
                json.dump(response.data, f, indent=2, ensure_ascii=False)

            logger.info(f"Generated processed JSON download file: {temp_path}")
            return temp_path

        except Exception as e:
            logger.error(f"Error generating processed JSON: {e}")
            return None

    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        with gr.Blocks(
            title=settings.ui.title,
            theme="default"
        ) as interface:
            
            gr.Markdown(f"# {settings.ui.title}")
            gr.Markdown(settings.ui.description)

            with gr.Tabs():
                with gr.Tab("🚀 Extraction"):
                    with gr.Row():
                        with gr.Column(scale=1):
                            gr.Markdown("## Configuration")

                            # File selection
                            available_files = self.get_available_files()
                            file_dropdown = gr.Dropdown(
                                choices=available_files,
                                value=available_files[0] if available_files and not available_files[0].startswith(("No files", "Database", "Error")) else None,
                                label="Select PDF File",
                                info="Choose a PDF file from the database",
                                interactive=True
                            )

                            refresh_files_btn = gr.Button("🔄 Refresh Files", size="sm")

                            # Extraction type
                            extraction_types = self.get_extraction_types()
                            extraction_dropdown = gr.Dropdown(
                                choices=extraction_types,
                                value=extraction_types[0] if extraction_types else None,
                                label="Extraction Type",
                                info="Choose the type of data to extract"
                            )

                            # Model selection
                            available_models = self.get_available_models()
                            model_choices = []
                            for provider, models in available_models.items():
                                model_choices.extend(models)

                            # Set default model value
                            default_model = None
                            if settings.models.default_model in model_choices:
                                default_model = settings.models.default_model
                            elif model_choices:
                                default_model = model_choices[0]  # Use first available model as fallback

                            model_dropdown = gr.Dropdown(
                                choices=model_choices,
                                label="LLM Model",
                                value=default_model,
                                info="Choose the language model for extraction"
                            )

                            # Options
                            with gr.Accordion("Advanced Options", open=False):
                                use_search_queries = gr.Checkbox(
                                    label="Use Search Queries",
                                    value=True,
                                    info="Use predefined search queries for better chunk retrieval"
                                )

                                max_chunks = gr.Slider(
                                    minimum=1,
                                    maximum=30,
                                    value=settings.extraction.max_chunks,
                                    step=1,
                                    label="Max Chunks",
                                    info="Maximum number of chunks to process"
                                )

                                extraction_method = gr.Dropdown(
                                    choices=["auto", "batch", "incremental"],
                                    value="auto",
                                    label="Extraction Method",
                                    info="auto: recommended method, batch: all chunks at once, incremental: one chunk at a time with memory"
                                )

                            # Action buttons
                            extract_btn = gr.Button("🚀 Extract Data", variant="primary", size="lg")

                            with gr.Row():
                                download_json_btn = gr.DownloadButton("📥 Download JSON", size="sm", visible=False)
                                download_csv_btn = gr.DownloadButton("📥 Download CSV", size="sm", visible=False)

                        with gr.Column(scale=2):
                            gr.Markdown("## Results")

                            # Status display
                            status_display = gr.Textbox(
                                label="Status",
                                value="Ready to extract data",
                                interactive=False,
                                elem_classes=["status-box"]
                            )

                            # Page numbers display
                            page_numbers_display = gr.Textbox(
                                label="Pages",
                                value="",
                                interactive=False
                            )

                            # Results tabs
                            with gr.Tabs():
                                with gr.Tab("📋 Extracted Data"):
                                    results_json = gr.Code(
                                        label="JSON Results",
                                        lines=20
                                    )

                                with gr.Tab("📊 CSV Preview"):
                                    results_csv = gr.Code(
                                        label="CSV Results",
                                        lines=20
                                    )

                                with gr.Tab("📄 Chunk Preview"):
                                    chunk_preview = gr.Markdown(
                                        value="No chunks loaded",
                                        label="Retrieved Chunks Preview"
                                    )



                with gr.Tab("⚙️ Configuration Editor"):
                    gr.Markdown("## Edit Extraction Configuration")
                    gr.Markdown("Modify schemas, system prompts, and search queries for different extraction types.")

                    with gr.Row():
                        config_extraction_type = gr.Dropdown(
                            choices=self.get_extraction_types(),
                            value=self.get_extraction_types()[0] if self.get_extraction_types() else None,
                            label="Select Extraction Type to Edit",
                            info="Choose which configuration to modify"
                        )
                        load_config_btn = gr.Button("📥 Load Configuration", size="sm")

                    with gr.Row():
                        with gr.Column():
                            schema_editor = gr.Code(
                                label="JSON Schema - Define the structure of extracted data",
                                language="json",
                                lines=15
                            )

                        with gr.Column():
                            prompt_editor = gr.Textbox(
                                label="System Prompt - Instructions for the AI model",
                                lines=15
                            )

                    queries_editor = gr.Textbox(
                        label="Search Queries - Search terms used to find relevant content in PDFs (one per line)",
                        lines=10
                    )

                    with gr.Row():
                        save_config_btn = gr.Button("💾 Save Configuration", variant="primary")
                        config_status = gr.Textbox(
                            label="Configuration Status",
                            value="Ready to edit configuration",
                            interactive=False
                        )

                with gr.Tab("📤 Upload PDF"):
                    gr.Markdown("## Upload and Process PDF Documents")
                    gr.Markdown("Upload PDF files to process them with Mistral OCR and store in the database for extraction.")

                    with gr.Row():
                        with gr.Column(scale=1):
                            pdf_upload = gr.File(
                                label="Select PDF File",
                                file_types=[".pdf"],
                                file_count="single"
                            )

                            upload_btn = gr.Button("🚀 Process PDF", variant="primary", size="lg")

                            # gr.Markdown("### Processing Options")
                            # gr.Markdown("- **Mistral OCR**: Advanced OCR with image extraction")
                            # gr.Markdown("- **Text Cleaning**: Removes pipes and extra whitespace to reduce token count")
                            # gr.Markdown("- **LangSmith Tracking**: Monitors token usage and costs (if configured)")

                        with gr.Column(scale=2):
                            gr.Markdown("## Processing Status")

                            upload_status = gr.Textbox(
                                label="Status",
                                value="Ready to process PDF",
                                interactive=False,
                                elem_classes=["status-box"]
                            )

                            upload_progress = gr.Textbox(
                                label="Progress",
                                value="",
                                interactive=False,
                                lines=10
                            )

                            processing_stats = gr.JSON(
                                label="Processing Statistics",
                                value={}
                            )

                            # Download options
                            with gr.Row():
                                pdf_download_csv_btn = gr.DownloadButton("📊 Download CSV", size="sm", visible=False)
                                pdf_download_json_btn = gr.DownloadButton("💾 Download JSON", size="sm", visible=False)



            # Event handlers
            def refresh_files():
                available_files = self.get_available_files()
                return gr.Dropdown(
                    choices=available_files,
                    value=available_files[0] if available_files and not available_files[0].startswith(("No files", "Database", "Error")) else None
                )

            refresh_files_btn.click(
                fn=refresh_files,
                outputs=file_dropdown
            )
            
            extract_btn.click(
                fn=self.perform_extraction,
                inputs=[file_dropdown, extraction_dropdown, model_dropdown, use_search_queries, max_chunks, extraction_method],
                outputs=[status_display, results_json, results_csv]
            ).then(
                fn=self.get_chunk_preview,
                outputs=chunk_preview
            ).then(
                fn=self.get_page_numbers,
                outputs=page_numbers_display
            ).then(
                fn=self.update_download_buttons,
                outputs=[download_json_btn, download_csv_btn]
            )
            


            # PDF upload event handler
            upload_btn.click(
                fn=self.process_pdf_upload,
                inputs=pdf_upload,
                outputs=[upload_status, upload_progress, processing_stats, pdf_download_csv_btn, pdf_download_json_btn]
            )



            # Configuration editor event handlers
            # Auto-load configuration when extraction type changes
            config_extraction_type.change(
                fn=self.get_config_for_editing,
                inputs=config_extraction_type,
                outputs=[schema_editor, prompt_editor, queries_editor]
            )

            # Manual load button for refresh
            load_config_btn.click(
                fn=self.get_config_for_editing,
                inputs=config_extraction_type,
                outputs=[schema_editor, prompt_editor, queries_editor]
            )

            save_config_btn.click(
                fn=self.save_config_changes,
                inputs=[config_extraction_type, schema_editor, prompt_editor, queries_editor],
                outputs=config_status
            )

        return interface


def create_app() -> gr.Blocks:
    """Create and return the Gradio application."""
    app = ExtractionApp()
    return app.create_interface()


def launch_app(share: bool = False, debug: bool = False):
    """Launch the Gradio application."""
    try:
        # Validate configuration
        issues = settings.validate_configuration()
        if issues:
            logger.error("Configuration issues found:")
            for issue in issues:
                logger.error(f"  - {issue}")
            raise RuntimeError("Configuration validation failed")
        
        # Create and launch app
        app = create_app()

        # Simple authentication function
        def authenticate(username, password):
            valid_users = {
                "admin": "marine2024",
                "user": "extract123",
                "demo": "demo123"
            }
            return valid_users.get(username) == password

        app.launch(
            share=share or settings.ui.share,
            debug=debug or settings.ui.debug,
            server_name="0.0.0.0",
            server_port=7860,
            auth=authenticate
        )
        
    except Exception as e:
        logger.error(f"Failed to launch application: {e}")
        raise


if __name__ == "__main__":
    launch_app()
