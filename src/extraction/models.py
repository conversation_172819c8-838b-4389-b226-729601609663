#!/usr/bin/env python3
"""
Auto-generated Pydantic models from JSON schemas.

This file is automatically generated by the SchemaMigrator.
Do not edit manually - changes will be overwritten.

Generated on: 2025-07-16T21:13:56.023419
"""

from typing import List, Optional, Union, Dict, Any, Literal
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

class SparePart(BaseModel):
    """Individual spare parts entry."""
    equipment_name: str = Field(
        description="Name of the equipment that the spare part belongs to"
    )

    part_name: str = Field(
        description="Name of the spare part for procurement"
    )

    part_number: str = Field(
        description="Part number associated with the spare part"
    )

    drawing_number: Optional[str] = Field(
        default=None,
        description="Drawing number from assembly drawings"
    )

    position_number: Optional[str] = Field(
        default=None,
        description="Position number from assembly drawings"
    )

    dimension: Optional[str] = Field(
        default=None,
        description="dimension of the spare part"
    )

    quantity: Optional[Union[str, Union[int, float]]] = Field(
        default=None,
        description="Working quantity of the spare part"
    )

    units: Optional[str] = Field(
        default=None,
        description="Units for the working quantity"
    )

    materials: Optional[str] = Field(
        default=None,
        description="Materials associated with the spare part"
    )

    remarks: Optional[str] = Field(
        default=None,
        description="Remarks associated with the spare part"
    )

    spare_part_title: Optional[str] = Field(
        default=None,
        description="Title of the spare parts table"
    )

    pdf_reference: Union[int, float] = Field(
        description="PDF page number where the spare part details are found"
    )

class SparePartsList(BaseModel):
    """Collection of spare parts entries."""
    spare_parts: List[SparePart] = Field(
        description="List of spare parts found in the document"
    )

class Job(BaseModel):
    """Individual job extraction entry."""
    equipment_name: str = Field(
        description="Major system name (e.g., Air Compressor, Freshwater Generator)"
    )

    job_body: str = Field(
        description="Component or sub-system (e.g., Valves, Oil Filter, Cylinder, General Check)"
    )

    job_action: str = Field(
        description="Maintenance activity (e.g., Inspect, Replace, Clean, Check, Service)"
    )

    frequency: str = Field(
        description="Numeric value only (e.g., 1, 250, 1000, 2000, 8000, 9000)"
    )

    frequency_type: Literal["Hours", "Days", "Weeks", "Months", "Years"] = Field(
        description="One of: Hours, Days, Weeks, Months, Years"
    )

    job_procedure: str = Field(
        description="Combined procedures in bullet list format using HTML <br> tags (e.g., '- Inspect piping for leaks.<br>- Check oil level.<br>- Observe cooling water and noise levels.')"
    )

    pdf_reference: str = Field(
        description="Section title or page number (e.g., 'Section 7.1, Table 6', 'Page 15', 'Maintenance Schedule Table')"
    )

class JobsList(BaseModel):
    """Collection of job extraction entries."""
    maintenance_jobs: List[Job] = Field(
        description="List of job extraction found in the document"
    )

class Component(BaseModel):
    """Individual component extraction entry."""
    equipment_name: str = Field(
        description="Equipment name following parent-child hierarchy (e.g., Motor - Freshwater Pump - Freshwater Generator)"
    )

    maker_name: str = Field(
        description="Manufacturer name (mark as N/A if unavailable)"
    )

    model: str = Field(
        description="Model/type number (separate multiple models with |, mark as N/A if missing)"
    )

    serial_number: str = Field(
        description="Serial or manufacturer number (mark as N/A if missing)"
    )

    particulars: str = Field(
        description="Technical specs like power, capacity, flow, pressure, voltage, dimensions"
    )

    motor_capacity: str = Field(
        description="Motor rating in kW/HP (if applicable, else N/A)"
    )

    quantity: str = Field(
        description="Number of identical units onboard"
    )

    component_type: str = Field(
        description="Component category (e.g., Centrifugal Pump, Gearbox, Ejector, Panel, Electric Motor)"
    )

    pdf_reference: str = Field(
        description="Sequential page number(s) starting from 1 (separate multiple pages with |)"
    )

    spare_pages: str = Field(
        description="Pages listing spare parts (only map once per unique equipment, separate with |)"
    )

    job_pages: str = Field(
        description="Pages listing maintenance/inspection/service procedures (separate with |)"
    )

class ComponentsList(BaseModel):
    """Collection of component extraction entries."""
    components: List[Component] = Field(
        description="List of component extraction found in the document"
    )

# Model mapping for extraction types
MODEL_MAPPING = {
    "spare_parts": SparePartsList,
    "job_extraction": JobsList,
    "component_extraction": ComponentsList,
}


def get_model_for_extraction_type(extraction_type: str) -> type[BaseModel]:
    """Get the appropriate Pydantic model for an extraction type."""
    if extraction_type not in MODEL_MAPPING:
        raise ValueError(f"Unsupported extraction type: {extraction_type}")
    return MODEL_MAPPING[extraction_type]


def validate_extraction_data(data: dict, extraction_type: str) -> BaseModel:
    """Validate extraction data against the appropriate model."""
    model_class = get_model_for_extraction_type(extraction_type)
    
    try:
        return model_class(**data)
    except Exception as e:
        logger.error(f"Validation failed for {extraction_type}: {e}")
        raise ValueError(f"Data validation failed: {e}")


def model_to_dict_list(model: BaseModel) -> List[dict]:
    """Convert a Pydantic model to a list of dictionaries for export."""
    # Get the first field which should be the array field
    fields = list(model.model_fields.keys())
    if not fields:
        return []
    
    array_field = getattr(model, fields[0])
    if isinstance(array_field, list):
        return [item.model_dump() for item in array_field]
    else:
        return [model.model_dump()]


def get_model_schema(extraction_type: str) -> dict:
    """Get the JSON schema for an extraction type."""
    model_class = get_model_for_extraction_type(extraction_type)
    return model_class.model_json_schema()
