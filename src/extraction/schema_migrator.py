#!/usr/bin/env python3
"""
Schema Migration System: JSON Schema → Pydantic Models → CSV

This module automatically generates Pydantic models from JSON schemas,
ensuring perfect synchronization between schema definitions and data models.
"""

import json
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class SchemaMigrator:
    """Migrates JSON schemas to Pydantic models automatically."""
    
    def __init__(self, schemas_dir: Path, models_output_file: Path):
        self.schemas_dir = Path(schemas_dir)
        self.models_output_file = Path(models_output_file)
        
    def python_type_from_json_type(self, json_type: Union[str, List[str]], 
                                   enum_values: Optional[List] = None) -> str:
        """Convert JSON schema type to Python type annotation."""
        if isinstance(json_type, list):
            # Handle union types like ["string", "number"]
            types = [self.python_type_from_json_type(t) for t in json_type]
            return f"Union[{', '.join(types)}]"
        
        type_mapping = {
            "string": "str",
            "number": "Union[int, float]",
            "integer": "int", 
            "boolean": "bool",
            "array": "List[str]",  # Default to List[str], can be customized
            "object": "Dict[str, Any]"
        }
        
        if enum_values:
            # Create Literal type for enums
            enum_str = ", ".join([f'"{v}"' for v in enum_values])
            return f"Literal[{enum_str}]"
            
        return type_mapping.get(json_type, "str")
    
    def sanitize_field_name(self, name: str) -> str:
        """Convert field name to valid Python identifier."""
        # Replace invalid characters with underscores
        sanitized = re.sub(r'[^\w]', '_', name)
        # Ensure it doesn't start with a number
        if sanitized[0].isdigit():
            sanitized = f"field_{sanitized}"
        return sanitized
    
    def generate_pydantic_field(self, field_name: str, field_schema: Dict[str, Any], 
                               required: bool = False) -> str:
        """Generate a Pydantic field definition from JSON schema property."""
        sanitized_name = self.sanitize_field_name(field_name)
        
        # Get type information
        field_type = field_schema.get("type", "string")
        enum_values = field_schema.get("enum")
        description = field_schema.get("description", f"{field_name} field")
        
        # Generate Python type
        python_type = self.python_type_from_json_type(field_type, enum_values)
        
        # Make optional if not required
        if not required:
            python_type = f"Optional[{python_type}]"
        
        # Generate Field definition
        field_def = f'    {sanitized_name}: {python_type} = Field(\n'
        
        if not required:
            field_def += f'        default=None,\n'
        
        field_def += f'        description="{description}"\n'
        field_def += f'    )'
        
        return field_def
    
    def extract_model_info_from_schema(self, schema: Dict[str, Any], 
                                      extraction_type: str) -> Dict[str, Any]:
        """Extract model information from JSON schema."""
        # Handle different schema structures
        if "properties" in schema:
            # Direct object schema
            properties = schema["properties"]
            required_fields = schema.get("required", [])
            
            # Find the main array field (items, components, spare_parts, etc.)
            main_array_field = None
            for prop_name, prop_schema in properties.items():
                if prop_schema.get("type") == "array":
                    main_array_field = prop_name
                    break
            
            if main_array_field:
                item_schema = properties[main_array_field]["items"]
                return {
                    "item_properties": item_schema.get("properties", {}),
                    "item_required": item_schema.get("required", []),
                    "array_field_name": main_array_field,
                    "model_name": self.get_model_name(extraction_type),
                    "list_model_name": self.get_list_model_name(extraction_type)
                }
        
        elif "type" in schema and schema["type"] == "array":
            # Direct array schema
            item_schema = schema["items"]
            return {
                "item_properties": item_schema.get("properties", {}),
                "item_required": item_schema.get("required", []),
                "array_field_name": self.get_array_field_name(extraction_type),
                "model_name": self.get_model_name(extraction_type),
                "list_model_name": self.get_list_model_name(extraction_type)
            }
        
        raise ValueError(f"Unsupported schema structure for {extraction_type}")
    
    def get_model_name(self, extraction_type: str) -> str:
        """Generate model class name from extraction type."""
        name_mapping = {
            "spare_parts": "SparePart",
            "component_extraction": "Component", 
            "job_extraction": "Job"
        }
        return name_mapping.get(extraction_type, extraction_type.title().replace("_", ""))
    
    def get_list_model_name(self, extraction_type: str) -> str:
        """Generate list model class name from extraction type."""
        name_mapping = {
            "spare_parts": "SparePartsList",
            "component_extraction": "ComponentsList",
            "job_extraction": "JobsList"
        }
        return name_mapping.get(extraction_type, f"{self.get_model_name(extraction_type)}List")
    
    def get_array_field_name(self, extraction_type: str) -> str:
        """Generate array field name from extraction type."""
        name_mapping = {
            "spare_parts": "spare_parts",
            "component_extraction": "components",
            "job_extraction": "jobs"
        }
        return name_mapping.get(extraction_type, "items")
    
    def generate_model_code(self, extraction_type: str, model_info: Dict[str, Any]) -> str:
        """Generate complete Pydantic model code."""
        model_name = model_info["model_name"]
        list_model_name = model_info["list_model_name"]
        array_field_name = model_info["array_field_name"]
        properties = model_info["item_properties"]
        required_fields = model_info["item_required"]
        
        # Generate individual item model
        item_model = f'class {model_name}(BaseModel):\n'
        item_model += f'    """Individual {extraction_type.replace("_", " ")} entry."""\n'
        
        for field_name, field_schema in properties.items():
            is_required = field_name in required_fields
            field_code = self.generate_pydantic_field(field_name, field_schema, is_required)
            item_model += field_code + '\n\n'
        
        # Generate list model
        list_model = f'class {list_model_name}(BaseModel):\n'
        list_model += f'    """Collection of {extraction_type.replace("_", " ")} entries."""\n'
        list_model += f'    {array_field_name}: List[{model_name}] = Field(\n'
        list_model += f'        description="List of {extraction_type.replace("_", " ")} found in the document"\n'
        list_model += f'    )\n\n'
        
        return item_model + list_model
    
    def generate_complete_models_file(self) -> str:
        """Generate complete models.py file from all schemas."""
        header = '''#!/usr/bin/env python3
"""
Auto-generated Pydantic models from JSON schemas.

This file is automatically generated by the SchemaMigrator.
Do not edit manually - changes will be overwritten.

Generated on: {timestamp}
"""

from typing import List, Optional, Union, Dict, Any, Literal
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

'''.format(timestamp=datetime.now().isoformat())
        
        models_code = header
        model_mapping = {}
        
        # Process each schema file
        for schema_file in self.schemas_dir.glob("*.json"):
            extraction_type = schema_file.stem
            
            try:
                with open(schema_file, 'r', encoding='utf-8') as f:
                    schema = json.load(f)
                
                model_info = self.extract_model_info_from_schema(schema, extraction_type)
                model_code = self.generate_model_code(extraction_type, model_info)
                models_code += model_code
                
                # Store mapping for later use
                model_mapping[extraction_type] = {
                    "item_model": model_info["model_name"],
                    "list_model": model_info["list_model_name"]
                }
                
                logger.info(f"Generated models for {extraction_type}")
                
            except Exception as e:
                logger.error(f"Failed to process schema {schema_file}: {e}")
                continue
        
        # Add utility functions
        models_code += self.generate_utility_functions(model_mapping)
        
        return models_code
    
    def generate_utility_functions(self, model_mapping: Dict[str, Dict[str, str]]) -> str:
        """Generate utility functions for model handling."""
        # Create model mapping dictionary
        mapping_code = "# Model mapping for extraction types\n"
        mapping_code += "MODEL_MAPPING = {\n"
        
        for extraction_type, models in model_mapping.items():
            mapping_code += f'    "{extraction_type}": {models["list_model"]},\n'
        
        mapping_code += "}\n\n"
        
        # Utility functions
        utility_code = '''
def get_model_for_extraction_type(extraction_type: str) -> type[BaseModel]:
    """Get the appropriate Pydantic model for an extraction type."""
    if extraction_type not in MODEL_MAPPING:
        raise ValueError(f"Unsupported extraction type: {extraction_type}")
    return MODEL_MAPPING[extraction_type]


def validate_extraction_data(data: dict, extraction_type: str) -> BaseModel:
    """Validate extraction data against the appropriate model."""
    model_class = get_model_for_extraction_type(extraction_type)
    
    try:
        return model_class(**data)
    except Exception as e:
        logger.error(f"Validation failed for {extraction_type}: {e}")
        raise ValueError(f"Data validation failed: {e}")


def model_to_dict_list(model: BaseModel) -> List[dict]:
    """Convert a Pydantic model to a list of dictionaries for export."""
    # Get the first field which should be the array field
    fields = list(model.model_fields.keys())
    if not fields:
        return []
    
    array_field = getattr(model, fields[0])
    if isinstance(array_field, list):
        return [item.model_dump() for item in array_field]
    else:
        return [model.model_dump()]


def get_model_schema(extraction_type: str) -> dict:
    """Get the JSON schema for an extraction type."""
    model_class = get_model_for_extraction_type(extraction_type)
    return model_class.model_json_schema()
'''
        
        return mapping_code + utility_code
    
    def migrate_schemas(self) -> bool:
        """Migrate all JSON schemas to Pydantic models."""
        try:
            logger.info("Starting schema migration...")
            
            # Generate models file
            models_content = self.generate_complete_models_file()
            
            # Write to file
            self.models_output_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.models_output_file, 'w', encoding='utf-8') as f:
                f.write(models_content)
            
            logger.info(f"Successfully generated models file: {self.models_output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Schema migration failed: {e}")
            return False


def main():
    """Run schema migration."""
    schemas_dir = Path("data/schemas")
    models_file = Path("src/extraction/models_generated.py")
    
    migrator = SchemaMigrator(schemas_dir, models_file)
    success = migrator.migrate_schemas()
    
    if success:
        print("✅ Schema migration completed successfully!")
        print(f"Generated models file: {models_file}")
    else:
        print("❌ Schema migration failed!")


if __name__ == "__main__":
    main()
