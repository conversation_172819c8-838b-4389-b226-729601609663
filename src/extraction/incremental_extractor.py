#!/usr/bin/env python3
"""
Incremental LLM-based data extractor with memory using LangChain.

This module provides structured data extraction from PDF chunks one at a time,
using <PERSON><PERSON><PERSON><PERSON>'s memory features to maintain context across chunks.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_mistralai import ChatMistralAI
from langchain_anthropic import ChatAnthropic
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from langchain_core.messages import HumanMessage, SystemMessage
from langchain.memory import ConversationSummaryBufferMemory
from langchain.schema import BaseMessage
from pydantic import BaseModel

from ..core.base_classes import BaseExtractor, ChunkData, ExtractionResult, TokenUsage, ExtractionType, ExtractionError
from ..core.utils import clean_text_for_processing, calculate_cost_estimate
from ..config.settings import settings
from .models import get_model_for_extraction_type, validate_extraction_data, model_to_dict_list

logger = logging.getLogger(__name__)


class IncrementalExtractor(BaseExtractor):
    """Incremental LLM-based data extractor with memory using LangChain."""
    
    def __init__(self, model_name: Optional[str] = None, temperature: float = 0.1, 
                 max_memory_tokens: int = 2000):
        """
        Initialize incremental LLM extractor.
        
        Args:
            model_name: Name of the model to use (defaults to settings)
            temperature: Temperature for generation (defaults to 0.1)
            max_memory_tokens: Maximum tokens to keep in memory buffer
        """
        self.model_name = model_name or settings.models.default_model
        self.temperature = temperature
        self.max_memory_tokens = max_memory_tokens
        self.provider = settings.get_model_provider(self.model_name)
        
        # Initialize LLM
        self.llm = self._create_llm()
        
        # Initialize memory
        self.memory = None
        self._reset_memory()
        
        logger.info(f"Initialized incremental LLM extractor with model: {self.model_name}")
    
    def _create_llm(self):
        """Create LangChain LLM instance based on provider."""
        try:
            if self.provider == "openai":
                if not settings.models.openai_api_key:
                    raise ExtractionError("OpenAI API key not configured")
                return ChatOpenAI(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.openai_api_key,
                )
            
            elif self.provider == "anthropic":
                if not settings.models.anthropic_api_key:
                    raise ExtractionError("Anthropic API key not configured")
                return ChatAnthropic(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.anthropic_api_key,
                    max_tokens=32000
                )
            
            elif self.provider == "mistral":
                if not settings.models.mistral_api_key:
                    raise ExtractionError("Mistral API key not configured")
                return ChatMistralAI(
                    model=self.model_name,
                    temperature=self.temperature,
                    api_key=settings.models.mistral_api_key,
                    max_tokens=32000
                )
            
            else:
                raise ExtractionError(f"Unsupported provider: {self.provider}")
                
        except Exception as e:
            raise ExtractionError(f"Failed to create LLM: {e}")
    
    def _reset_memory(self):
        """Reset the conversation memory."""
        self.memory = ConversationSummaryBufferMemory(
            llm=self.llm,
            max_token_limit=self.max_memory_tokens,
            return_messages=True
        )
    
    def extract(self, chunks: List[ChunkData], extraction_type: ExtractionType, 
                system_prompt: str, schema: Dict[str, Any]) -> ExtractionResult:
        """
        Extract structured data from chunks incrementally using memory.
        
        Args:
            chunks: List of chunks to process
            extraction_type: Type of extraction
            system_prompt: System prompt for extraction
            schema: JSON schema (not used with structured output)
            
        Returns:
            Extraction result with structured data
        """
        if not chunks:
            return ExtractionResult(
                success=False,
                error="No chunks provided for extraction",
                extraction_method="incremental_langchain"
            )
        
        try:
            # Reset memory for new extraction
            self._reset_memory()
            
            # Get the appropriate Pydantic model
            model_class = get_model_for_extraction_type(extraction_type.value)
            
            # Create structured LLM
            structured_llm = self.llm.with_structured_output(model_class)
            
            # Initialize tracking
            total_input_tokens = 0
            total_output_tokens = 0
            all_extracted_items = []
            
            logger.info(f"Starting incremental extraction with {len(chunks)} chunks using {self.model_name}")
            
            # Log the page order for incremental processing
            page_numbers = [chunk.page_number for chunk in chunks]
            logger.info(f"Processing {len(chunks)} chunks in page order: {page_numbers}")

            # Process chunks one at a time
            for i, chunk in enumerate(chunks, 1):
                logger.info(f"Processing chunk {i}/{len(chunks)} from page {chunk.page_number}")
                
                # Prepare chunk context
                chunk_context = self._prepare_chunk_context(chunk)
                
                # Get memory context
                memory_context = self._get_memory_context()
                
                # Create incremental prompt
                prompt_template = self._create_incremental_prompt(
                    system_prompt, extraction_type, i, len(chunks)
                )
                
                # Estimate input tokens for this chunk
                input_tokens = self._estimate_chunk_tokens(chunk_context, memory_context, system_prompt)
                total_input_tokens += input_tokens
                
                # Create extraction chain
                extraction_chain = prompt_template | structured_llm
                
                # Perform extraction for this chunk
                try:
                    result = extraction_chain.invoke({
                        "chunk_context": chunk_context,
                        "memory_context": memory_context,
                        "chunk_number": i,
                        "total_chunks": len(chunks),
                        "page_number": chunk.page_number
                    })
                    
                    # Extract items from result
                    chunk_items = self._extract_items_from_result(result, extraction_type)
                    all_extracted_items.extend(chunk_items)
                    
                    # Estimate output tokens
                    output_tokens = self._estimate_output_tokens(result)
                    total_output_tokens += output_tokens
                    
                    # Update memory with this chunk's processing
                    self._update_memory(chunk, chunk_items, i)
                    
                    logger.info(f"Chunk {i}: extracted {len(chunk_items)} items (tokens: {input_tokens}→{output_tokens})")
                    
                except Exception as e:
                    logger.warning(f"Failed to process chunk {i} from page {chunk.page_number}: {e}")
                    continue
            
            # Create final result
            final_result = self._create_final_result(all_extracted_items, extraction_type)
            
            # Calculate total token usage
            token_usage = TokenUsage(
                input_tokens=total_input_tokens,
                output_tokens=total_output_tokens,
                total_tokens=total_input_tokens + total_output_tokens,
                estimated_cost_usd=calculate_cost_estimate(
                    TokenUsage(total_input_tokens, total_output_tokens, 
                             total_input_tokens + total_output_tokens),
                    self.model_name
                )
            )
            
            logger.info(f"Incremental extraction completed: {len(all_extracted_items)} total items extracted")
            
            return ExtractionResult(
                success=True,
                data=model_to_dict_list(final_result),
                token_usage=token_usage,
                extraction_method="incremental_langchain",
                total_chunks=len(chunks),
                metadata={
                    "chunks_processed": len(chunks),
                    "items_extracted": len(all_extracted_items),
                    "model_name": self.model_name,
                    "extraction_type": extraction_type.value,
                    "processing_method": "incremental_with_memory"
                }
            )
            
        except Exception as e:
            logger.error(f"Incremental extraction failed: {e}")
            return ExtractionResult(
                success=False,
                error=str(e),
                extraction_method="incremental_langchain",
                total_chunks=len(chunks)
            )
    
    def _prepare_chunk_context(self, chunk: ChunkData) -> str:
        """Prepare context for a single chunk."""
        cleaned_text = clean_text_for_processing(chunk.text_raw)
        return f"[Page {chunk.page_number}]\n{cleaned_text}"
    
    def _get_memory_context(self) -> str:
        """Get current memory context as string."""
        if not self.memory or not self.memory.chat_memory.messages:
            return "No previous context."
        
        # Get memory summary
        try:
            memory_summary = self.memory.predict_new_summary(
                self.memory.chat_memory.messages, ""
            )
            return f"Previous context summary: {memory_summary}"
        except:
            return "Previous context available but summary unavailable."
    
    def _create_incremental_prompt(self, system_prompt: str, extraction_type: ExtractionType,
                                 chunk_num: int, total_chunks: int) -> ChatPromptTemplate:
        """Create prompt template for incremental processing."""
        # Escape curly braces in system prompt to prevent LangChain template variable conflicts
        escaped_system_prompt = system_prompt.replace("{", "{{").replace("}", "}}")

        # Create the incremental instructions separately to avoid double-escaping
        incremental_instructions = f"""
INCREMENTAL PROCESSING INSTRUCTIONS:
- You are processing chunk {chunk_num} of {total_chunks} total chunks
- Extract ONLY the data present in the current chunk
- Use the memory context to understand what has been processed before
- Do not duplicate items that may have been extracted from previous chunks
- Focus on NEW information in this chunk
- If no relevant data is found in this chunk, return an empty list

MEMORY CONTEXT:
{{memory_context}}

CURRENT CHUNK (Page {{page_number}}):
{{chunk_context}}"""

        # Combine the escaped system prompt with incremental instructions
        full_system_prompt = escaped_system_prompt + incremental_instructions

        return ChatPromptTemplate.from_messages([
            ("system", full_system_prompt),
            ("human", "Extract structured data from the current chunk only. Return empty list if no relevant data found.")
        ])
    
    def _extract_items_from_result(self, result: BaseModel, extraction_type: ExtractionType) -> List[Dict]:
        """Extract items list from the structured result."""
        if extraction_type == ExtractionType.SPARE_PARTS:
            return [item.model_dump() for item in result.spare_parts]
        elif extraction_type == ExtractionType.COMPONENT_EXTRACTION:
            return [item.model_dump() for item in result.components]
        elif extraction_type == ExtractionType.JOB_EXTRACTION:
            return [item.model_dump() for item in result.jobs]
        else:
            return []
    
    def _create_final_result(self, all_items: List[Dict], extraction_type: ExtractionType) -> BaseModel:
        """Create final result model from all extracted items."""
        model_class = get_model_for_extraction_type(extraction_type.value)
        
        if extraction_type == ExtractionType.SPARE_PARTS:
            from .models import SparePart, SparePartsList
            spare_parts = [SparePart(**item) for item in all_items]
            return SparePartsList(spare_parts=spare_parts)
        elif extraction_type == ExtractionType.COMPONENT_EXTRACTION:
            from .models import Component, ComponentsList
            components = [Component(**item) for item in all_items]
            return ComponentsList(components=components)
        elif extraction_type == ExtractionType.JOB_EXTRACTION:
            from .models import Job, JobsList
            jobs = [Job(**item) for item in all_items]
            return JobsList(jobs=jobs)
        else:
            raise ValueError(f"Unsupported extraction type: {extraction_type}")
    
    def _update_memory(self, chunk: ChunkData, extracted_items: List[Dict], chunk_num: int):
        """Update memory with processed chunk information."""
        summary = f"Processed chunk {chunk_num} from page {chunk.page_number}: extracted {len(extracted_items)} items"
        
        # Add to memory
        self.memory.chat_memory.add_user_message(f"Processed page {chunk.page_number}")
        self.memory.chat_memory.add_ai_message(summary)
    
    def _estimate_chunk_tokens(self, chunk_context: str, memory_context: str, system_prompt: str) -> int:
        """Estimate tokens for a single chunk processing."""
        total_text = chunk_context + memory_context + system_prompt
        return len(total_text.split()) * 1.3  # Rough estimation
    
    def _estimate_output_tokens(self, result: BaseModel) -> int:
        """Estimate output tokens from result."""
        result_text = str(result.model_dump())
        return len(result_text.split()) * 1.3  # Rough estimation
