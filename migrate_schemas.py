#!/usr/bin/env python3
"""
Schema Migration CLI Tool

This script migrates JSON schemas to Pydantic models and optionally
replaces the existing models.py file.

Usage:
    python migrate_schemas.py                    # Generate models_generated.py
    python migrate_schemas.py --replace          # Replace existing models.py
    python migrate_schemas.py --test             # Test generated models
    python migrate_schemas.py --compare          # Compare with existing models
"""

import argparse
import shutil
from pathlib import Path
import sys
import importlib.util

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from src.extraction.schema_migrator import SchemaMigrator


def test_generated_models(models_file: Path) -> bool:
    """Test the generated models by importing and running basic tests."""
    try:
        print("🧪 Testing generated models...")
        
        # Import the generated models
        spec = importlib.util.spec_from_file_location("models_generated", models_file)
        models_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(models_module)
        
        # Test each extraction type
        test_data = {
            "spare_parts": {
                "spare_parts": [{
                    "equipment_name": "Test Equipment",
                    "part_name": "Test Part", 
                    "part_number": "TP-001",
                    "dimension": "10x20x5mm",
                    "pdf_reference": 1
                }]
            },
            "component_extraction": {
                "components": [{
                    "equipment_name": "Test Component",
                    "maker_name": "Test Maker",
                    "model": "TM-001",
                    "serial_number": "SN-001",
                    "particulars": "Test specs",
                    "motor_capacity": "5kW",
                    "quantity": "1",
                    "component_type": "Motor",
                    "pdf_reference": "1",
                    "spare_pages": "2",
                    "job_pages": "3"
                }]
            },
            "job_extraction": {
                "maintenance_jobs": [{
                    "equipment_name": "Test Equipment",
                    "job_body": "Test Body",
                    "job_action": "Inspect",
                    "frequency": "100",
                    "frequency_type": "Hours",
                    "job_procedure": "Test procedure",
                    "pdf_reference": "Page 1"
                }]
            }
        }
        
        for extraction_type, data in test_data.items():
            try:
                # Test model validation
                model = models_module.validate_extraction_data(data, extraction_type)
                print(f"✅ {extraction_type}: Model validation passed")
                
                # Test dict conversion
                dict_list = models_module.model_to_dict_list(model)
                print(f"✅ {extraction_type}: Dict conversion passed ({len(dict_list)} items)")
                
                # Check for dimension field in spare_parts
                if extraction_type == "spare_parts" and dict_list:
                    if 'dimension' in dict_list[0]:
                        print(f"✅ {extraction_type}: Dimension field present")
                    else:
                        print(f"❌ {extraction_type}: Dimension field missing")
                        return False
                        
            except Exception as e:
                print(f"❌ {extraction_type}: Test failed - {e}")
                return False
        
        print("✅ All model tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return False


def compare_models(old_file: Path, new_file: Path) -> None:
    """Compare old and new models files."""
    print("🔍 Comparing model files...")
    
    if not old_file.exists():
        print(f"❌ Old models file not found: {old_file}")
        return
    
    if not new_file.exists():
        print(f"❌ New models file not found: {new_file}")
        return
    
    # Simple line count comparison
    with open(old_file, 'r') as f:
        old_lines = len(f.readlines())
    
    with open(new_file, 'r') as f:
        new_lines = len(f.readlines())
    
    print(f"📊 Old models file: {old_lines} lines")
    print(f"📊 New models file: {new_lines} lines")
    
    # Check for key differences
    try:
        # Import both modules for comparison
        spec_old = importlib.util.spec_from_file_location("models_old", old_file)
        old_module = importlib.util.module_from_spec(spec_old)
        spec_old.loader.exec_module(old_module)
        
        spec_new = importlib.util.spec_from_file_location("models_new", new_file)
        new_module = importlib.util.module_from_spec(spec_new)
        spec_new.loader.exec_module(new_module)
        
        # Compare SparePart fields
        if hasattr(old_module, 'SparePart') and hasattr(new_module, 'SparePart'):
            old_fields = set(old_module.SparePart.model_fields.keys())
            new_fields = set(new_module.SparePart.model_fields.keys())
            
            added_fields = new_fields - old_fields
            removed_fields = old_fields - new_fields
            
            if added_fields:
                print(f"➕ Added fields: {added_fields}")
            if removed_fields:
                print(f"➖ Removed fields: {removed_fields}")
            if not added_fields and not removed_fields:
                print("✅ SparePart fields are identical")
        
    except Exception as e:
        print(f"⚠️  Could not compare model details: {e}")


def main():
    parser = argparse.ArgumentParser(description="Migrate JSON schemas to Pydantic models")
    parser.add_argument("--replace", action="store_true", 
                       help="Replace existing models.py with generated models")
    parser.add_argument("--test", action="store_true",
                       help="Test the generated models")
    parser.add_argument("--compare", action="store_true", 
                       help="Compare with existing models.py")
    
    args = parser.parse_args()
    
    # Paths
    schemas_dir = Path("data/schemas")
    generated_models_file = Path("src/extraction/models_generated.py")
    existing_models_file = Path("src/extraction/models.py")
    backup_models_file = Path("src/extraction/models_backup.py")
    
    # Run migration
    print("🚀 Starting schema migration...")
    migrator = SchemaMigrator(schemas_dir, generated_models_file)
    
    if not migrator.migrate_schemas():
        print("❌ Migration failed!")
        return 1
    
    print(f"✅ Generated models: {generated_models_file}")
    
    # Test generated models
    if args.test or args.replace:
        if not test_generated_models(generated_models_file):
            print("❌ Generated models failed tests!")
            return 1
    
    # Compare models
    if args.compare:
        compare_models(existing_models_file, generated_models_file)
    
    # Replace existing models
    if args.replace:
        print("🔄 Replacing existing models.py...")
        
        # Create backup
        if existing_models_file.exists():
            shutil.copy2(existing_models_file, backup_models_file)
            print(f"📦 Backup created: {backup_models_file}")
        
        # Replace with generated models
        shutil.copy2(generated_models_file, existing_models_file)
        print(f"✅ Replaced {existing_models_file}")
        
        # Clean up generated file
        generated_models_file.unlink()
        print("🧹 Cleaned up temporary generated file")
    
    print("\n🎉 Schema migration completed successfully!")
    print("\n📋 Next steps:")
    print("1. Review the generated models")
    print("2. Test your extraction pipeline")
    print("3. Verify CSV exports include all expected columns")
    
    if not args.replace:
        print(f"4. Run with --replace to update models.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
