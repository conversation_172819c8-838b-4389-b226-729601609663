<<<<<<< HEAD
---
title: Marine Equipment Data Builder
emoji: 🚢
colorFrom: blue
colorTo: green
sdk: gradio
sdk_version: 5.0.0
app_file: app.py
pinned: false
license: mit
---

# 🚢 Marine Equipment Data Builder

An intelligent PDF processing and structured data extraction system specifically designed for marine equipment documentation. Extract spare parts, job procedures, and component specifications from technical manuals using AI-powered document analysis.

## 🚀 Quick Start

This Hugging Face Space provides an easy-to-use interface for extracting structured data from marine equipment PDFs. Simply:

1. **Upload a PDF** - Technical manuals, parts catalogs, or maintenance documents
2. **Choose extraction type** - Spare parts, job procedures, or component specifications
3. **Select AI model** - OpenAI GPT-4, Claude, or Mistral models
4. **Get structured data** - Download results as JSON or CSV

## ⚙️ Configuration

To use this space, you'll need to set your **OPENAI_API_KEY** in the Hugging Face Spaces secrets. Other AI providers (Anthropic, Mistral) are optional but can be configured for enhanced functionality.

## 🌟 Key Features

### 🔍 Intelligent Document Processing
- **Advanced PDF Parsing**: Extract text, tables, and images with high accuracy
- **Smart Chunking**: Preserve document context and structure
- **Vector Search**: Find relevant content using semantic similarity
- **Multi-format Support**: Handle complex technical documentation

### 🎯 Specialized Extraction Types
- **Spare Parts**: Extract part numbers, descriptions, specifications, and pricing
- **Job Procedures**: Identify maintenance tasks, steps, and requirements
- **Component Specs**: Capture equipment specifications and technical details
- **Custom Schemas**: Define your own extraction patterns

### 🤖 Multiple AI Models
- **OpenAI GPT-4**: High-accuracy extraction with advanced reasoning
- **Claude (Anthropic)**: Excellent for technical document analysis
- **Mistral**: Cost-effective option with good performance
- **Auto-selection**: System recommends best model for your task

## 📋 How to Use

### 1. Upload Your PDF
Click the upload area and select a marine equipment PDF document. Supported formats include:
- Technical manuals
- Parts catalogs
- Maintenance procedures
- Equipment specifications

### 2. Choose Extraction Type
Select what type of data you want to extract:
- **Spare Parts**: Part numbers, descriptions, prices, availability
- **Job Procedures**: Maintenance tasks, steps, tools required
- **Component Specs**: Equipment specifications, features, models

### 3. Configure Settings
- **AI Model**: Choose between GPT-4, Claude, or Mistral
- **Max Chunks**: Limit processing for faster results
- **Search Method**: Use semantic search for better accuracy

### 4. Extract & Download
- Click "Extract Data" to process your document
- View results in JSON format
- Download as CSV for spreadsheet use
- Save results for future reference

## 💡 Example Use Cases

### Marine Parts Inventory
- Extract spare parts from equipment catalogs
- Build searchable parts databases
- Track part numbers and specifications
- Generate procurement lists

### Maintenance Planning
- Extract maintenance procedures from manuals
- Identify required tools and materials
- Schedule preventive maintenance tasks
- Create work order templates

### Equipment Documentation
- Catalog technical specifications
- Extract performance parameters
- Document safety procedures
- Create equipment databases

## 🔧 Technical Details

### Architecture
- **Frontend**: Gradio web interface with modern UI
- **Backend**: Python with LangChain integration
- **Database**: Supabase with pgvector for similarity search
- **AI Models**: OpenAI, Anthropic, and Mistral APIs
- **Processing**: Advanced PDF parsing with table/image extraction

### Data Processing Pipeline
1. **PDF Upload**: Secure file handling and validation
2. **Content Extraction**: Text, tables, and images using multiple parsers
3. **Chunking**: Smart document segmentation preserving context
4. **Embedding**: Vector representations for semantic search
5. **AI Extraction**: Structured data extraction using LLMs
6. **Export**: JSON and CSV format downloads

### Supported Document Types
- Equipment manuals and catalogs
- Technical specifications
- Maintenance procedures
- Parts lists and inventories
- Safety documentation
- Installation guides

## 🚨 Important Notes

### API Keys Required
This application requires an **OpenAI API key** to function. Set it in the Hugging Face Spaces secrets:
1. Go to your Space settings
2. Add `OPENAI_API_KEY` as a secret
3. Restart the Space

### Data Privacy
- Uploaded PDFs are processed temporarily and not permanently stored
- Extracted data is available for download during your session
- No personal data is retained after session ends

### Usage Limits
- File size limit: 50MB per PDF
- Processing time varies based on document complexity
- API usage costs apply based on your OpenAI plan

## 🛠️ For Developers

### Local Development
```bash
git clone https://huggingface.co/spaces/anujmv/marine-data-builder
cd marine-data-builder
pip install -r requirements.txt
python app.py
```

### Environment Variables
```bash
OPENAI_API_KEY=your_openai_key_here
SUPABASE_URL=https://uscmgehdwxnupiercoqn.supabase.co
SUPABASE_KEY=your_supabase_key_here
```

### Contributing
Contributions are welcome! Please feel free to submit issues or pull requests to improve the functionality.

## 📄 License

MIT License - Open source and free to use.

---

**Built with ❤️ using Gradio, OpenAI, Supabase, and modern Python tools for the marine industry.**
=======
# 🔍 Gradio Docling RAG - PDF Processing & Structured Query System

A comprehensive PDF processing and structured data extraction system using Docling, OpenAI, and Supabase with pgvector for intelligent document analysis.

## 🆕 New Modular System

The system now includes a completely redesigned modular architecture (`src/` directory) that provides:

- **Modern LangChain Integration**: Uses latest `.with_structured_output()` patterns
- **Modular Architecture**: Clean separation of concerns across multiple modules
- **Multiple AI Providers**: Support for OpenAI, Anthropic, and Mistral models
- **Comprehensive Testing**: Full test suite with 95%+ coverage
- **CLI and Web Interfaces**: Both command-line and Gradio web interfaces
- **Advanced Error Handling**: Robust error handling with recovery strategies
- **Token Tracking**: Detailed usage monitoring and cost estimation
- **LangSmith Integration**: Optional tracing and performance monitoring

### Quick Start with New System

```bash
# Launch web interface
python src/main.py --mode web

# CLI extraction
python src/main.py --mode cli --extraction-type spare_parts --file-name "document.pdf"

# System validation
python src/main.py --validate
```

## 🌟 Features

### PDF Ingestion (`ingest_pdf.py`)
- **PDF Parsing**: Extract text page-by-page using `pdfplumber`
- **Table Detection**: Automatically detect and structure tables using OpenAI GPT-4
- **Image Extraction**: Extract and save all images using `PyMuPDF`
- **Smart Chunking**: Store each page as a single chunk with metadata
- **Vector Embeddings**: Generate embeddings using OpenAI `text-embedding-3-small`
- **Database Storage**: Store in Supabase with pgvector for similarity search

### Structured Query Interface (`structured_query.py`)
- **JSON Schema Support**: Define extraction schemas for structured data
- **Natural Language Instructions**: Describe what to extract in plain English
- **File-Specific Search**: Limit queries to specific PDF documents
- **Intelligent Subquery Generation**: Break complex queries into focused searches
- **Vector Similarity Search**: Find relevant content using semantic search
- **Structured Data Extraction**: Extract JSON data conforming to your schema
- **CSV Export**: Download results as CSV files

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone and setup
git clone <repository>
cd gradio-docling-rag

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Update your `.env` file with:
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key

### 3. Ingest a PDF

```bash
python ingest_pdf.py "sample/TANK CLEANING MACHINE.pdf"
```

This will:
- Process all 62 pages
- Extract 690+ images
- Detect 41 pages with tables
- Generate embeddings and store in Supabase

### 4. Launch Structured Query Interface

```bash
python structured_query.py
```

Open http://localhost:7860 in your browser.

## 📊 Database Schema

### PDF Documents Table
```sql
CREATE TABLE pdf_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_name TEXT,
    page_number INTEGER,
    text TEXT,                    -- Full markdown content
    metadata JSONB,               -- Table info, image paths, etc.
    embedding VECTOR(1536),       -- OpenAI embedding
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Similarity Search Function
```sql
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float,
    match_count int,
    input_file_name text
) RETURNS TABLE (
    id uuid,
    file_name text,
    page_number int,
    text text,
    metadata jsonb,
    similarity float
);
```

## 🎯 Usage Examples

### Example 1: Extract Equipment Specifications

**JSON Schema:**
```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "equipment_name": {"type": "string"},
      "model": {"type": "string"},
      "specifications": {"type": "string"},
      "features": {"type": "string"}
    }
  }
}
```

**Instruction:**
"Extract all tank cleaning equipment with their models, specifications, and key features."

### Example 2: Extract Parts Information

**JSON Schema:**
```json
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "part_number": {"type": "string"},
      "description": {"type": "string"},
      "price": {"type": "string"},
      "availability": {"type": "string"}
    }
  }
}
```

**Instruction:**
"Find all spare parts with their part numbers, descriptions, prices, and availability status."

## 🧪 Testing

### Run Unit Tests
```bash
python test_ingest_pdf.py --unit
python test_structured_query.py
```

### Run Integration Tests
```bash
python test_ingest_pdf.py --integration
```

## 📁 Project Structure

```
gradio-docling-rag/
├── ingest_pdf.py              # PDF processing and ingestion
├── structured_query.py        # Gradio app for structured queries
├── test_ingest_pdf.py         # Tests for PDF ingestion
├── test_structured_query.py   # Tests for structured queries
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── README.md                 # This file
├── sample/                   # Sample PDF files
│   └── TANK CLEANING MACHINE.pdf
└── images/                   # Extracted images (auto-created)
    ├── page1_img1.png
    ├── page1_img2.png
    └── ...
```

## 🔧 Key Components

### PDF Processing Pipeline
1. **Text Extraction**: `pdfplumber` for accurate text extraction
2. **Table Detection**: Automatic table detection and GPT-4 structuring
3. **Image Extraction**: `PyMuPDF` for comprehensive image extraction
4. **Markdown Generation**: Structured markdown with tables, images, and text
5. **Vector Embeddings**: OpenAI embeddings for semantic search
6. **Database Storage**: Supabase with pgvector for efficient retrieval

### Query Processing Pipeline
1. **Schema Validation**: Validate JSON schema format
2. **Subquery Generation**: Break complex queries into focused searches
3. **Vector Search**: Find relevant content using similarity search
4. **Context Aggregation**: Combine relevant chunks intelligently
5. **Structured Extraction**: GPT-4 extracts data conforming to schema
6. **Result Formatting**: JSON output with CSV export option

## 🎨 Gradio Interface Features

- **Interactive Schema Editor**: JSON schema input with syntax highlighting
- **Natural Language Instructions**: Describe extraction requirements
- **File Selection**: Choose specific PDF documents to query
- **Real-time Status**: See processing progress and results
- **JSON Viewer**: Formatted JSON output with syntax highlighting
- **CSV Export**: Download structured data as CSV files

## 🔍 Advanced Features

### Metadata Enrichment
Each page includes rich metadata:
- Table detection and structuring
- Image paths and references
- Page-level context preservation
- Continuation detection for multi-page tables

### Smart Chunking Strategy
- **Page-level chunks**: Preserve complete page context
- **Table preservation**: Keep tables with surrounding text
- **Image associations**: Link images to relevant content
- **Metadata integration**: Rich metadata for enhanced search

## 🚀 Performance

- **Processing Speed**: ~1-2 pages per second
- **Embedding Generation**: Batch processing for efficiency
- **Vector Search**: Sub-second similarity search
- **Scalability**: Handles large documents (60+ pages tested)

## 🛠️ Troubleshooting

### Common Issues

1. **OpenAI API Errors**: Check your API key and model availability
2. **Supabase Connection**: Verify URL and key in `.env` file
3. **Missing Dependencies**: Run `pip install -r requirements.txt`
4. **Image Extraction**: Ensure sufficient disk space for images

### Debug Mode
Set `DEBUG=true` in `.env` for verbose logging.

## 📈 Future Enhancements

- [ ] Support for multiple file formats (DOCX, PPTX)
- [ ] Advanced table extraction with cell-level precision
- [ ] Multi-language support
- [ ] Batch processing interface
- [ ] Custom embedding models
- [ ] Advanced query operators

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

---

**Built with ❤️ using Gradio, OpenAI, Supabase, and modern Python tools.**
>>>>>>> 70a62b853fcbed68048550567120b29348618c98
