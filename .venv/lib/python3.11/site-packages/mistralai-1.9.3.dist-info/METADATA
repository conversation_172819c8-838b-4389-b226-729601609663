Metadata-Version: 2.3
Name: mistralai
Version: 1.9.3
Summary: Python Client SDK for the Mistral AI API.
Author: Mistral
Requires-Python: >=3.9
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Provides-Extra: agents
Provides-Extra: gcp
Requires-Dist: authlib (>=1.5.2,<2.0) ; extra == "agents"
Requires-Dist: eval-type-backport (>=0.2.0)
Requires-Dist: google-auth (>=2.27.0) ; extra == "gcp"
Requires-Dist: griffe (>=1.7.3,<2.0) ; extra == "agents"
Requires-Dist: httpx (>=0.28.1)
Requires-Dist: mcp (>=1.0,<2.0) ; (python_version >= "3.10") and (extra == "agents")
Requires-Dist: pydantic (>=2.10.3)
Requires-Dist: python-dateutil (>=2.8.2)
Requires-Dist: requests (>=2.32.3) ; extra == "gcp"
Requires-Dist: typing-inspection (>=0.4.0)
Project-URL: Repository, https://github.com/mistralai/client-python.git
Description-Content-Type: text/markdown

# Mistral Python Client

## Migration warning
 
This documentation is for Mistral AI SDK v1. You can find more details on how to migrate from v0 to v1 [here](https://github.com/mistralai/client-python/blob/master/MIGRATION.md)

## API Key Setup

Before you begin, you will need a Mistral AI API key.

1. Get your own Mistral API Key: <https://docs.mistral.ai/#api-access>
2. Set your Mistral API Key as an environment variable. You only need to do this once.

```bash
# set Mistral API Key (using zsh for example)
$ echo 'export MISTRAL_API_KEY=[your_key_here]' >> ~/.zshenv

# reload the environment (or just quit and open a new terminal)
$ source ~/.zshenv
```

<!-- Start Summary [summary] -->
## Summary

Mistral AI API: Our Chat Completion and Embeddings APIs specification. Create your account on [La Plateforme](https://console.mistral.ai) to get access and read the [docs](https://docs.mistral.ai) to learn how to use it.
<!-- End Summary [summary] -->

<!-- Start Table of Contents [toc] -->
## Table of Contents
<!-- $toc-max-depth=2 -->
* [Mistral Python Client](https://github.com/mistralai/client-python/blob/master/#mistral-python-client)
  * [Migration warning](https://github.com/mistralai/client-python/blob/master/#migration-warning)
  * [API Key Setup](https://github.com/mistralai/client-python/blob/master/#api-key-setup)
  * [SDK Installation](https://github.com/mistralai/client-python/blob/master/#sdk-installation)
  * [SDK Example Usage](https://github.com/mistralai/client-python/blob/master/#sdk-example-usage)
  * [Providers' SDKs Example Usage](https://github.com/mistralai/client-python/blob/master/#providers-sdks-example-usage)
  * [Available Resources and Operations](https://github.com/mistralai/client-python/blob/master/#available-resources-and-operations)
  * [Server-sent event streaming](https://github.com/mistralai/client-python/blob/master/#server-sent-event-streaming)
  * [File uploads](https://github.com/mistralai/client-python/blob/master/#file-uploads)
  * [Retries](https://github.com/mistralai/client-python/blob/master/#retries)
  * [Error Handling](https://github.com/mistralai/client-python/blob/master/#error-handling)
  * [Server Selection](https://github.com/mistralai/client-python/blob/master/#server-selection)
  * [Custom HTTP Client](https://github.com/mistralai/client-python/blob/master/#custom-http-client)
  * [Authentication](https://github.com/mistralai/client-python/blob/master/#authentication)
  * [Resource Management](https://github.com/mistralai/client-python/blob/master/#resource-management)
  * [Debugging](https://github.com/mistralai/client-python/blob/master/#debugging)
  * [IDE Support](https://github.com/mistralai/client-python/blob/master/#ide-support)
* [Development](https://github.com/mistralai/client-python/blob/master/#development)
  * [Contributions](https://github.com/mistralai/client-python/blob/master/#contributions)

<!-- End Table of Contents [toc] -->

<!-- Start SDK Installation [installation] -->
## SDK Installation

> [!NOTE]
> **Python version upgrade policy**
>
> Once a Python version reaches its [official end of life date](https://devguide.python.org/versions/), a 3-month grace period is provided for users to upgrade. Following this grace period, the minimum python version supported in the SDK will be updated.

The SDK can be installed with either *pip* or *poetry* package managers.

### PIP

*PIP* is the default package installer for Python, enabling easy installation and management of packages from PyPI via the command line.

```bash
pip install mistralai
```

### Poetry

*Poetry* is a modern tool that simplifies dependency management and package publishing by using a single `pyproject.toml` file to handle project metadata and dependencies.

```bash
poetry add mistralai
```

### Shell and script usage with `uv`

You can use this SDK in a Python shell with [uv](https://docs.astral.sh/uv/) and the `uvx` command that comes with it like so:

```shell
uvx --from mistralai python
```

It's also possible to write a standalone Python script without needing to set up a whole project like so:

```python
#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.9"
# dependencies = [
#     "mistralai",
# ]
# ///

from mistralai import Mistral

sdk = Mistral(
  # SDK arguments
)

# Rest of script here...
```

Once that is saved to a file, you can run it with `uv run script.py` where
`script.py` can be replaced with the actual file name.
<!-- End SDK Installation [installation] -->

### Agents extra dependencies

When using the agents related feature it is required to add the `agents` extra dependencies. This can be added when 
installing the package:

```bash
pip install "mistralai[agents]"
```

> Note: Because of some of our dependencies, these features are only available for python version higher or equal to 
> 3.10.

<!-- Start SDK Example Usage [usage] -->
## SDK Example Usage

### Create Chat Completions

This example shows how to create chat completions.

```python
# Synchronous Example
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.chat.complete(model="mistral-small-latest", messages=[
        {
            "content": "Who is the best French painter? Answer in one short sentence.",
            "role": "user",
        },
    ], stream=False)

    # Handle response
    print(res)
```

</br>

The same SDK client can also be used to make asychronous requests by importing asyncio.
```python
# Asynchronous Example
import asyncio
from mistralai import Mistral
import os

async def main():

    async with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:

        res = await mistral.chat.complete_async(model="mistral-small-latest", messages=[
            {
                "content": "Who is the best French painter? Answer in one short sentence.",
                "role": "user",
            },
        ], stream=False)

        # Handle response
        print(res)

asyncio.run(main())
```

### Upload a file

This example shows how to upload a file.

```python
# Synchronous Example
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.files.upload(file={
        "file_name": "example.file",
        "content": open("example.file", "rb"),
    })

    # Handle response
    print(res)
```

</br>

The same SDK client can also be used to make asychronous requests by importing asyncio.
```python
# Asynchronous Example
import asyncio
from mistralai import Mistral
import os

async def main():

    async with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:

        res = await mistral.files.upload_async(file={
            "file_name": "example.file",
            "content": open("example.file", "rb"),
        })

        # Handle response
        print(res)

asyncio.run(main())
```

### Create Agents Completions

This example shows how to create agents completions.

```python
# Synchronous Example
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.agents.complete(messages=[
        {
            "content": "Who is the best French painter? Answer in one short sentence.",
            "role": "user",
        },
    ], agent_id="<id>", stream=False)

    # Handle response
    print(res)
```

</br>

The same SDK client can also be used to make asychronous requests by importing asyncio.
```python
# Asynchronous Example
import asyncio
from mistralai import Mistral
import os

async def main():

    async with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:

        res = await mistral.agents.complete_async(messages=[
            {
                "content": "Who is the best French painter? Answer in one short sentence.",
                "role": "user",
            },
        ], agent_id="<id>", stream=False)

        # Handle response
        print(res)

asyncio.run(main())
```

### Create Embedding Request

This example shows how to create embedding request.

```python
# Synchronous Example
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.embeddings.create(model="mistral-embed", inputs=[
        "Embed this sentence.",
        "As well as this one.",
    ])

    # Handle response
    print(res)
```

</br>

The same SDK client can also be used to make asychronous requests by importing asyncio.
```python
# Asynchronous Example
import asyncio
from mistralai import Mistral
import os

async def main():

    async with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:

        res = await mistral.embeddings.create_async(model="mistral-embed", inputs=[
            "Embed this sentence.",
            "As well as this one.",
        ])

        # Handle response
        print(res)

asyncio.run(main())
```
<!-- End SDK Example Usage [usage] -->


### More examples

You can run the examples in the `examples/` directory using `poetry run` or by entering the virtual environment using `poetry shell`.


## Providers' SDKs Example Usage

### Azure AI

**Prerequisites**

Before you begin, ensure you have `AZUREAI_ENDPOINT` and an `AZURE_API_KEY`. To obtain these, you will need to deploy Mistral on Azure AI.
See [instructions for deploying Mistral on Azure AI here](https://docs.mistral.ai/deployment/cloud/azure/).

Here's a basic example to get you started. You can also run [the example in the `examples` directory](https://github.com/mistralai/client-python/blob/master//examples/azure).

```python
import asyncio
import os

from mistralai_azure import MistralAzure

client = MistralAzure(
    azure_api_key=os.getenv("AZURE_API_KEY", ""),
    azure_endpoint=os.getenv("AZURE_ENDPOINT", "")
)

async def main() -> None:
    res = await client.chat.complete_async( 
        max_tokens= 100,
        temperature= 0.5,
        messages= [
            {
                "content": "Hello there!",
                "role": "user"
            }
        ]
    )
    print(res)

asyncio.run(main())
```
The documentation for the Azure SDK is available [here](https://github.com/mistralai/client-python/blob/master/packages/mistralai_azure/README.md).

### Google Cloud


**Prerequisites**

Before you begin, you will need to create a Google Cloud project and enable the Mistral API. To do this, follow the instructions [here](https://docs.mistral.ai/deployment/cloud/vertex/).

To run this locally you will also need to ensure you are authenticated with Google Cloud. You can do this by running

```bash
gcloud auth application-default login
```

**Step 1: Install**

Install the extras dependencies specific to Google Cloud:

```bash
pip install mistralai[gcp]
```

**Step 2: Example Usage**

Here's a basic example to get you started.

```python
import asyncio
from mistralai_gcp import MistralGoogleCloud

client = MistralGoogleCloud()


async def main() -> None:
    res = await client.chat.complete_async(
        model= "mistral-small-2402",
        messages= [
            {
                "content": "Hello there!",
                "role": "user"
            }
        ]
    )
    print(res)

asyncio.run(main())
```

The documentation for the GCP SDK is available [here](https://github.com/mistralai/client-python/blob/master/packages/mistralai_gcp/README.md).


<!-- Start Available Resources and Operations [operations] -->
## Available Resources and Operations

<details open>
<summary>Available methods</summary>

### [agents](https://github.com/mistralai/client-python/blob/master/docs/sdks/agents/README.md)

* [complete](https://github.com/mistralai/client-python/blob/master/docs/sdks/agents/README.md#complete) - Agents Completion
* [stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/agents/README.md#stream) - Stream Agents completion

### [audio](https://github.com/mistralai/client-python/blob/master/docs/sdks/audio/README.md)


#### [audio.transcriptions](https://github.com/mistralai/client-python/blob/master/docs/sdks/transcriptions/README.md)

* [complete](https://github.com/mistralai/client-python/blob/master/docs/sdks/transcriptions/README.md#complete) - Create Transcription
* [stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/transcriptions/README.md#stream) - Create streaming transcription (SSE)

### [batch](https://github.com/mistralai/client-python/blob/master/docs/sdks/batch/README.md)


#### [batch.jobs](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistraljobs/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistraljobs/README.md#list) - Get Batch Jobs
* [create](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistraljobs/README.md#create) - Create Batch Job
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistraljobs/README.md#get) - Get Batch Job
* [cancel](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistraljobs/README.md#cancel) - Cancel Batch Job

### [beta](https://github.com/mistralai/client-python/blob/master/docs/sdks/beta/README.md)


#### [beta.agents](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md)

* [create](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md#create) - Create a agent that can be used within a conversation.
* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md#list) - List agent entities.
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md#get) - Retrieve an agent entity.
* [update](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md#update) - Update an agent entity.
* [update_version](https://github.com/mistralai/client-python/blob/master/docs/sdks/mistralagents/README.md#update_version) - Update an agent version.

#### [beta.conversations](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md)

* [start](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#start) - Create a conversation and append entries to it.
* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#list) - List all created conversations.
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#get) - Retrieve a conversation information.
* [append](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#append) - Append new entries to an existing conversation.
* [get_history](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#get_history) - Retrieve all entries in a conversation.
* [get_messages](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#get_messages) - Retrieve all messages in a conversation.
* [restart](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#restart) - Restart a conversation starting from a given entry.
* [start_stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#start_stream) - Create a conversation and append entries to it.
* [append_stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#append_stream) - Append new entries to an existing conversation.
* [restart_stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/conversations/README.md#restart_stream) - Restart a conversation starting from a given entry.

#### [beta.libraries](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md#list) - List all libraries you have access to.
* [create](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md#create) - Create a new Library.
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md#get) - Detailed information about a specific Library.
* [delete](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md#delete) - Delete a library and all of it's document.
* [update](https://github.com/mistralai/client-python/blob/master/docs/sdks/libraries/README.md#update) - Update a library.

#### [beta.libraries.accesses](https://github.com/mistralai/client-python/blob/master/docs/sdks/accesses/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/accesses/README.md#list) - List all of the access to this library.
* [update_or_create](https://github.com/mistralai/client-python/blob/master/docs/sdks/accesses/README.md#update_or_create) - Create or update an access level.
* [delete](https://github.com/mistralai/client-python/blob/master/docs/sdks/accesses/README.md#delete) - Delete an access level.

#### [beta.libraries.documents](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#list) - List document in a given library.
* [upload](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#upload) - Upload a new document.
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#get) - Retrieve the metadata of a specific document.
* [update](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#update) - Update the metadata of a specific document.
* [delete](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#delete) - Delete a document.
* [text_content](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#text_content) - Retrieve the text content of a specific document.
* [status](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#status) - Retrieve the processing status of a specific document.
* [get_signed_url](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#get_signed_url) - Retrieve the signed URL of a specific document.
* [extracted_text_signed_url](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#extracted_text_signed_url) - Retrieve the signed URL of text extracted from a given document.
* [reprocess](https://github.com/mistralai/client-python/blob/master/docs/sdks/documents/README.md#reprocess) - Reprocess a document.

### [chat](https://github.com/mistralai/client-python/blob/master/docs/sdks/chat/README.md)

* [complete](https://github.com/mistralai/client-python/blob/master/docs/sdks/chat/README.md#complete) - Chat Completion
* [stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/chat/README.md#stream) - Stream chat completion

### [classifiers](https://github.com/mistralai/client-python/blob/master/docs/sdks/classifiers/README.md)

* [moderate](https://github.com/mistralai/client-python/blob/master/docs/sdks/classifiers/README.md#moderate) - Moderations
* [moderate_chat](https://github.com/mistralai/client-python/blob/master/docs/sdks/classifiers/README.md#moderate_chat) - Chat Moderations
* [classify](https://github.com/mistralai/client-python/blob/master/docs/sdks/classifiers/README.md#classify) - Classifications
* [classify_chat](https://github.com/mistralai/client-python/blob/master/docs/sdks/classifiers/README.md#classify_chat) - Chat Classifications

### [embeddings](https://github.com/mistralai/client-python/blob/master/docs/sdks/embeddings/README.md)

* [create](https://github.com/mistralai/client-python/blob/master/docs/sdks/embeddings/README.md#create) - Embeddings

### [files](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md)

* [upload](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#upload) - Upload File
* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#list) - List Files
* [retrieve](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#retrieve) - Retrieve File
* [delete](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#delete) - Delete File
* [download](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#download) - Download File
* [get_signed_url](https://github.com/mistralai/client-python/blob/master/docs/sdks/files/README.md#get_signed_url) - Get Signed Url

### [fim](https://github.com/mistralai/client-python/blob/master/docs/sdks/fim/README.md)

* [complete](https://github.com/mistralai/client-python/blob/master/docs/sdks/fim/README.md#complete) - Fim Completion
* [stream](https://github.com/mistralai/client-python/blob/master/docs/sdks/fim/README.md#stream) - Stream fim completion

### [fine_tuning](https://github.com/mistralai/client-python/blob/master/docs/sdks/finetuning/README.md)


#### [fine_tuning.jobs](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md#list) - Get Fine Tuning Jobs
* [create](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md#create) - Create Fine Tuning Job
* [get](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md#get) - Get Fine Tuning Job
* [cancel](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md#cancel) - Cancel Fine Tuning Job
* [start](https://github.com/mistralai/client-python/blob/master/docs/sdks/jobs/README.md#start) - Start Fine Tuning Job


### [models](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md)

* [list](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#list) - List Models
* [retrieve](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#retrieve) - Retrieve Model
* [delete](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#delete) - Delete Model
* [update](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#update) - Update Fine Tuned Model
* [archive](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#archive) - Archive Fine Tuned Model
* [unarchive](https://github.com/mistralai/client-python/blob/master/docs/sdks/models/README.md#unarchive) - Unarchive Fine Tuned Model

### [ocr](https://github.com/mistralai/client-python/blob/master/docs/sdks/ocr/README.md)

* [process](https://github.com/mistralai/client-python/blob/master/docs/sdks/ocr/README.md#process) - OCR

</details>
<!-- End Available Resources and Operations [operations] -->

<!-- Start Server-sent event streaming [eventstream] -->
## Server-sent event streaming

[Server-sent events][mdn-sse] are used to stream content from certain
operations. These operations will expose the stream as [Generator][generator] that
can be consumed using a simple `for` loop. The loop will
terminate when the server no longer has any events to send and closes the
underlying connection.  

The stream is also a [Context Manager][context-manager] and can be used with the `with` statement and will close the
underlying connection when the context is exited.

```python
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.beta.conversations.start_stream(inputs=[
        {
            "object": "entry",
            "type": "function.result",
            "tool_call_id": "<id>",
            "result": "<value>",
        },
    ], stream=True)

    with res as event_stream:
        for event in event_stream:
            # handle event
            print(event, flush=True)

```

[mdn-sse]: https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events
[generator]: https://book.pythontips.com/en/latest/generators.html
[context-manager]: https://book.pythontips.com/en/latest/context_managers.html
<!-- End Server-sent event streaming [eventstream] -->

<!-- Start File uploads [file-upload] -->
## File uploads

Certain SDK methods accept file objects as part of a request body or multi-part request. It is possible and typically recommended to upload files as a stream rather than reading the entire contents into memory. This avoids excessive memory consumption and potentially crashing with out-of-memory errors when working with very large files. The following example demonstrates how to attach a file stream to a request.

> [!TIP]
>
> For endpoints that handle file uploads bytes arrays can also be used. However, using streams is recommended for large files.
>

```python
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.beta.libraries.documents.upload(library_id="a02150d9-5ee0-4877-b62c-28b1fcdf3b76", file={
        "file_name": "example.file",
        "content": open("example.file", "rb"),
    })

    # Handle response
    print(res)

```
<!-- End File uploads [file-upload] -->

<!-- Start Retries [retries] -->
## Retries

Some of the endpoints in this SDK support retries. If you use the SDK without any configuration, it will fall back to the default retry strategy provided by the API. However, the default retry strategy can be overridden on a per-operation basis, or across the entire SDK.

To change the default retry strategy for a single API call, simply provide a `RetryConfig` object to the call:
```python
from mistralai import Mistral
from mistralai.utils import BackoffStrategy, RetryConfig
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.models.list(,
        RetryConfig("backoff", BackoffStrategy(1, 50, 1.1, 100), False))

    # Handle response
    print(res)

```

If you'd like to override the default retry strategy for all operations that support retries, you can use the `retry_config` optional parameter when initializing the SDK:
```python
from mistralai import Mistral
from mistralai.utils import BackoffStrategy, RetryConfig
import os


with Mistral(
    retry_config=RetryConfig("backoff", BackoffStrategy(1, 50, 1.1, 100), False),
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.models.list()

    # Handle response
    print(res)

```
<!-- End Retries [retries] -->

<!-- Start Error Handling [errors] -->
## Error Handling

Handling errors in this SDK should largely match your expectations. All operations return a response object or raise an exception.

By default, an API error will raise a models.SDKError exception, which has the following properties:

| Property        | Type             | Description           |
|-----------------|------------------|-----------------------|
| `.status_code`  | *int*            | The HTTP status code  |
| `.message`      | *str*            | The error message     |
| `.raw_response` | *httpx.Response* | The raw HTTP response |
| `.body`         | *str*            | The response content  |

When custom error responses are specified for an operation, the SDK may also raise their associated exceptions. You can refer to respective *Errors* tables in SDK docs for more details on possible exception types for each operation. For example, the `list_async` method may raise the following exceptions:

| Error Type                 | Status Code | Content Type     |
| -------------------------- | ----------- | ---------------- |
| models.HTTPValidationError | 422         | application/json |
| models.SDKError            | 4XX, 5XX    | \*/\*            |

### Example

```python
from mistralai import Mistral, models
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:
    res = None
    try:

        res = mistral.models.list()

        # Handle response
        print(res)

    except models.HTTPValidationError as e:
        # handle e.data: models.HTTPValidationErrorData
        raise(e)
    except models.SDKError as e:
        # handle exception
        raise(e)
```
<!-- End Error Handling [errors] -->

<!-- Start Server Selection [server] -->
## Server Selection

### Select Server by Name

You can override the default server globally by passing a server name to the `server: str` optional parameter when initializing the SDK client instance. The selected server will then be used as the default on the operations that use it. This table lists the names associated with the available servers:

| Name | Server                   | Description          |
| ---- | ------------------------ | -------------------- |
| `eu` | `https://api.mistral.ai` | EU Production server |

#### Example

```python
from mistralai import Mistral
import os


with Mistral(
    server="eu",
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.models.list()

    # Handle response
    print(res)

```

### Override Server URL Per-Client

The default server can also be overridden globally by passing a URL to the `server_url: str` optional parameter when initializing the SDK client instance. For example:
```python
from mistralai import Mistral
import os


with Mistral(
    server_url="https://api.mistral.ai",
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.models.list()

    # Handle response
    print(res)

```
<!-- End Server Selection [server] -->

<!-- Start Custom HTTP Client [http-client] -->
## Custom HTTP Client

The Python SDK makes API calls using the [httpx](https://www.python-httpx.org/) HTTP library.  In order to provide a convenient way to configure timeouts, cookies, proxies, custom headers, and other low-level configuration, you can initialize the SDK client with your own HTTP client instance.
Depending on whether you are using the sync or async version of the SDK, you can pass an instance of `HttpClient` or `AsyncHttpClient` respectively, which are Protocol's ensuring that the client has the necessary methods to make API calls.
This allows you to wrap the client with your own custom logic, such as adding custom headers, logging, or error handling, or you can just pass an instance of `httpx.Client` or `httpx.AsyncClient` directly.

For example, you could specify a header for every request that this sdk makes as follows:
```python
from mistralai import Mistral
import httpx

http_client = httpx.Client(headers={"x-custom-header": "someValue"})
s = Mistral(client=http_client)
```

or you could wrap the client with your own custom logic:
```python
from mistralai import Mistral
from mistralai.httpclient import AsyncHttpClient
import httpx

class CustomClient(AsyncHttpClient):
    client: AsyncHttpClient

    def __init__(self, client: AsyncHttpClient):
        self.client = client

    async def send(
        self,
        request: httpx.Request,
        *,
        stream: bool = False,
        auth: Union[
            httpx._types.AuthTypes, httpx._client.UseClientDefault, None
        ] = httpx.USE_CLIENT_DEFAULT,
        follow_redirects: Union[
            bool, httpx._client.UseClientDefault
        ] = httpx.USE_CLIENT_DEFAULT,
    ) -> httpx.Response:
        request.headers["Client-Level-Header"] = "added by client"

        return await self.client.send(
            request, stream=stream, auth=auth, follow_redirects=follow_redirects
        )

    def build_request(
        self,
        method: str,
        url: httpx._types.URLTypes,
        *,
        content: Optional[httpx._types.RequestContent] = None,
        data: Optional[httpx._types.RequestData] = None,
        files: Optional[httpx._types.RequestFiles] = None,
        json: Optional[Any] = None,
        params: Optional[httpx._types.QueryParamTypes] = None,
        headers: Optional[httpx._types.HeaderTypes] = None,
        cookies: Optional[httpx._types.CookieTypes] = None,
        timeout: Union[
            httpx._types.TimeoutTypes, httpx._client.UseClientDefault
        ] = httpx.USE_CLIENT_DEFAULT,
        extensions: Optional[httpx._types.RequestExtensions] = None,
    ) -> httpx.Request:
        return self.client.build_request(
            method,
            url,
            content=content,
            data=data,
            files=files,
            json=json,
            params=params,
            headers=headers,
            cookies=cookies,
            timeout=timeout,
            extensions=extensions,
        )

s = Mistral(async_client=CustomClient(httpx.AsyncClient()))
```
<!-- End Custom HTTP Client [http-client] -->

<!-- Start Authentication [security] -->
## Authentication

### Per-Client Security Schemes

This SDK supports the following security scheme globally:

| Name      | Type | Scheme      | Environment Variable |
| --------- | ---- | ----------- | -------------------- |
| `api_key` | http | HTTP Bearer | `MISTRAL_API_KEY`    |

To authenticate with the API the `api_key` parameter must be set when initializing the SDK client instance. For example:
```python
from mistralai import Mistral
import os


with Mistral(
    api_key=os.getenv("MISTRAL_API_KEY", ""),
) as mistral:

    res = mistral.models.list()

    # Handle response
    print(res)

```
<!-- End Authentication [security] -->

<!-- Start Resource Management [resource-management] -->
## Resource Management

The `Mistral` class implements the context manager protocol and registers a finalizer function to close the underlying sync and async HTTPX clients it uses under the hood. This will close HTTP connections, release memory and free up other resources held by the SDK. In short-lived Python programs and notebooks that make a few SDK method calls, resource management may not be a concern. However, in longer-lived programs, it is beneficial to create a single SDK instance via a [context manager][context-manager] and reuse it across the application.

[context-manager]: https://docs.python.org/3/reference/datamodel.html#context-managers

```python
from mistralai import Mistral
import os
def main():

    with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:
        # Rest of application here...


# Or when using async:
async def amain():

    async with Mistral(
        api_key=os.getenv("MISTRAL_API_KEY", ""),
    ) as mistral:
        # Rest of application here...
```
<!-- End Resource Management [resource-management] -->

<!-- Start Debugging [debug] -->
## Debugging

You can setup your SDK to emit debug logs for SDK requests and responses.

You can pass your own logger class directly into your SDK.
```python
from mistralai import Mistral
import logging

logging.basicConfig(level=logging.DEBUG)
s = Mistral(debug_logger=logging.getLogger("mistralai"))
```

You can also enable a default debug logger by setting an environment variable `MISTRAL_DEBUG` to true.
<!-- End Debugging [debug] -->

<!-- Start IDE Support [idesupport] -->
## IDE Support

### PyCharm

Generally, the SDK will work well with most IDEs out of the box. However, when using PyCharm, you can enjoy much better integration with Pydantic by installing an additional plugin.

- [PyCharm Pydantic Plugin](https://docs.pydantic.dev/latest/integrations/pycharm/)
<!-- End IDE Support [idesupport] -->

<!-- Placeholder for Future Speakeasy SDK Sections -->

# Development

## Contributions

While we value open-source contributions to this SDK, this library is generated programmatically. Any manual changes added to internal files will be overwritten on the next generation. 
We look forward to hearing your feedback. Feel free to open a PR or an issue with a proof of concept and we'll do our best to include it in a future release. 
