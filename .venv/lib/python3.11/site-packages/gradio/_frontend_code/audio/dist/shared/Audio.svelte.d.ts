import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: `data-${string}`]: any;
        autoplay?: boolean | undefined | null;
        controls?: boolean | undefined | null;
        controlslist?: "nodownload" | "nofullscreen" | "noplaybackrate" | "noremoteplayback" | (string & {}) | undefined | null;
        crossorigin?: string | undefined | null;
        currenttime?: number | undefined | null;
        defaultmuted?: boolean | undefined | null;
        defaultplaybackrate?: number | undefined | null;
        loop?: boolean | undefined | null;
        mediagroup?: string | undefined | null;
        muted?: boolean | undefined | null;
        playsinline?: boolean | undefined | null;
        preload?: string | undefined | null;
        src?: string | undefined | null;
        volume?: number | undefined | null;
        readonly 'bind:readyState'?: 0 | 1 | 2 | 3 | 4 | undefined | null;
        readonly 'bind:duration'?: number | undefined | null;
        readonly 'bind:buffered'?: import("svelte/elements").SvelteMediaTimeRange[] | undefined | null;
        readonly 'bind:played'?: import("svelte/elements").SvelteMediaTimeRange[] | undefined | null;
        readonly 'bind:seekable'?: import("svelte/elements").SvelteMediaTimeRange[] | undefined | null;
        readonly 'bind:seeking'?: boolean | undefined | null;
        readonly 'bind:ended'?: boolean | undefined | null;
        'bind:muted'?: boolean | undefined | null;
        'bind:volume'?: number | undefined | null;
        'bind:currentTime'?: number | undefined | null;
        'bind:playbackRate'?: number | undefined | null;
        'bind:paused'?: boolean | undefined | null;
        accesskey?: string | undefined | null;
        autofocus?: boolean | undefined | null;
        class?: string | undefined | null;
        contenteditable?: import("svelte/elements").Booleanish | "inherit" | "plaintext-only" | undefined | null;
        contextmenu?: string | undefined | null;
        dir?: string | undefined | null;
        draggable?: import("svelte/elements").Booleanish | undefined | null;
        enterkeyhint?: "enter" | "done" | "go" | "next" | "previous" | "search" | "send" | undefined | null;
        hidden?: boolean | undefined | null;
        id?: string | undefined | null;
        lang?: string | undefined | null;
        part?: string | undefined | null;
        placeholder?: string | undefined | null;
        slot?: string | undefined | null;
        spellcheck?: import("svelte/elements").Booleanish | undefined | null;
        style?: string | undefined | null;
        tabindex?: number | undefined | null;
        title?: string | undefined | null;
        translate?: "yes" | "no" | "" | undefined | null;
        inert?: boolean | undefined | null;
        popover?: "auto" | "manual" | "" | undefined | null;
        radiogroup?: string | undefined | null;
        role?: import("svelte/elements").AriaRole | undefined | null;
        about?: string | undefined | null;
        datatype?: string | undefined | null;
        inlist?: any;
        prefix?: string | undefined | null;
        property?: string | undefined | null;
        resource?: string | undefined | null;
        typeof?: string | undefined | null;
        vocab?: string | undefined | null;
        autocapitalize?: string | undefined | null;
        autocorrect?: string | undefined | null;
        autosave?: string | undefined | null;
        color?: string | undefined | null;
        itemprop?: string | undefined | null;
        itemscope?: boolean | undefined | null;
        itemtype?: string | undefined | null;
        itemid?: string | undefined | null;
        itemref?: string | undefined | null;
        results?: number | undefined | null;
        security?: string | undefined | null;
        unselectable?: "on" | "off" | undefined | null;
        inputmode?: "none" | "text" | "tel" | "url" | "email" | "numeric" | "decimal" | "search" | undefined | null;
        is?: string | undefined | null;
        'bind:innerHTML'?: string | undefined | null;
        'bind:textContent'?: string | undefined | null;
        'bind:innerText'?: string | undefined | null;
        readonly 'bind:contentRect'?: DOMRectReadOnly | undefined | null;
        readonly 'bind:contentBoxSize'?: ResizeObserverSize[] | undefined | null;
        readonly 'bind:borderBoxSize'?: ResizeObserverSize[] | undefined | null;
        readonly 'bind:devicePixelContentBoxSize'?: ResizeObserverSize[] | undefined | null;
        'data-sveltekit-keepfocus'?: true | "" | "off" | undefined | null;
        'data-sveltekit-noscroll'?: true | "" | "off" | undefined | null;
        'data-sveltekit-preload-code'?: true | "" | "eager" | "viewport" | "hover" | "tap" | "off" | undefined | null;
        'data-sveltekit-preload-data'?: true | "" | "hover" | "tap" | "off" | undefined | null;
        'data-sveltekit-reload'?: true | "" | "off" | undefined | null;
        'data-sveltekit-replacestate'?: true | "" | "off" | undefined | null;
        'aria-activedescendant'?: string | undefined | null;
        'aria-atomic'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-autocomplete'?: "none" | "inline" | "list" | "both" | undefined | null;
        'aria-busy'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-checked'?: boolean | "false" | "mixed" | "true" | undefined | null;
        'aria-colcount'?: number | undefined | null;
        'aria-colindex'?: number | undefined | null;
        'aria-colspan'?: number | undefined | null;
        'aria-controls'?: string | undefined | null;
        'aria-current'?: import("svelte/elements").Booleanish | "page" | "step" | "location" | "date" | "time" | undefined | null;
        'aria-describedby'?: string | undefined | null;
        'aria-details'?: string | undefined | null;
        'aria-disabled'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-dropeffect'?: "none" | "copy" | "execute" | "link" | "move" | "popup" | undefined | null;
        'aria-errormessage'?: string | undefined | null;
        'aria-expanded'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-flowto'?: string | undefined | null;
        'aria-grabbed'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-haspopup'?: import("svelte/elements").Booleanish | "menu" | "listbox" | "tree" | "grid" | "dialog" | undefined | null;
        'aria-hidden'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-invalid'?: import("svelte/elements").Booleanish | "grammar" | "spelling" | undefined | null;
        'aria-keyshortcuts'?: string | undefined | null;
        'aria-label'?: string | undefined | null;
        'aria-labelledby'?: string | undefined | null;
        'aria-level'?: number | undefined | null;
        'aria-live'?: "off" | "assertive" | "polite" | undefined | null;
        'aria-modal'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-multiline'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-multiselectable'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-orientation'?: "horizontal" | "vertical" | undefined | null;
        'aria-owns'?: string | undefined | null;
        'aria-placeholder'?: string | undefined | null;
        'aria-posinset'?: number | undefined | null;
        'aria-pressed'?: boolean | "false" | "mixed" | "true" | undefined | null;
        'aria-readonly'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-relevant'?: "additions" | "additions removals" | "additions text" | "all" | "removals" | "removals additions" | "removals text" | "text" | "text additions" | "text removals" | undefined | null;
        'aria-required'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-roledescription'?: string | undefined | null;
        'aria-rowcount'?: number | undefined | null;
        'aria-rowindex'?: number | undefined | null;
        'aria-rowspan'?: number | undefined | null;
        'aria-selected'?: import("svelte/elements").Booleanish | undefined | null;
        'aria-setsize'?: number | undefined | null;
        'aria-sort'?: "none" | "ascending" | "descending" | "other" | undefined | null;
        'aria-valuemax'?: number | undefined | null;
        'aria-valuemin'?: number | undefined | null;
        'aria-valuenow'?: number | undefined | null;
        'aria-valuetext'?: string | undefined | null;
        'on:copy'?: import("svelte/elements").ClipboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:cut'?: import("svelte/elements").ClipboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:paste'?: import("svelte/elements").ClipboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:compositionend'?: import("svelte/elements").CompositionEventHandler<HTMLAudioElement> | null | undefined;
        'on:compositionstart'?: import("svelte/elements").CompositionEventHandler<HTMLAudioElement> | null | undefined;
        'on:compositionupdate'?: import("svelte/elements").CompositionEventHandler<HTMLAudioElement> | null | undefined;
        'on:focus'?: import("svelte/elements").FocusEventHandler<HTMLAudioElement> | null | undefined;
        'on:focusin'?: import("svelte/elements").FocusEventHandler<HTMLAudioElement> | null | undefined;
        'on:focusout'?: import("svelte/elements").FocusEventHandler<HTMLAudioElement> | null | undefined;
        'on:blur'?: import("svelte/elements").FocusEventHandler<HTMLAudioElement> | null | undefined;
        'on:change'?: import("svelte/elements").FormEventHandler<HTMLAudioElement> | null | undefined;
        'on:beforeinput'?: import("svelte/elements").EventHandler<InputEvent, HTMLAudioElement> | null | undefined;
        'on:input'?: import("svelte/elements").FormEventHandler<HTMLAudioElement> | null | undefined;
        'on:reset'?: import("svelte/elements").FormEventHandler<HTMLAudioElement> | null | undefined;
        'on:submit'?: import("svelte/elements").EventHandler<SubmitEvent, HTMLAudioElement> | null | undefined;
        'on:invalid'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:formdata'?: import("svelte/elements").EventHandler<FormDataEvent, HTMLAudioElement> | null | undefined;
        'on:load'?: import("svelte/elements").EventHandler | undefined | null;
        'on:error'?: import("svelte/elements").EventHandler | undefined | null;
        'on:beforetoggle'?: import("svelte/elements").ToggleEventHandler<HTMLAudioElement> | null | undefined;
        'on:toggle'?: import("svelte/elements").ToggleEventHandler<HTMLAudioElement> | null | undefined;
        'on:keydown'?: import("svelte/elements").KeyboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:keypress'?: import("svelte/elements").KeyboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:keyup'?: import("svelte/elements").KeyboardEventHandler<HTMLAudioElement> | null | undefined;
        'on:abort'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:canplay'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:canplaythrough'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:cuechange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:durationchange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:emptied'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:encrypted'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:ended'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:loadeddata'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:loadedmetadata'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:loadstart'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:pause'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:play'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:playing'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:progress'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:ratechange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:seeked'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:seeking'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:stalled'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:suspend'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:timeupdate'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:volumechange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:waiting'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:auxclick'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:click'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:contextmenu'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:dblclick'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:drag'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragend'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragenter'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragexit'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragleave'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragover'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:dragstart'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:drop'?: import("svelte/elements").DragEventHandler<HTMLAudioElement> | null | undefined;
        'on:mousedown'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mouseenter'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mouseleave'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mousemove'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mouseout'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mouseover'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:mouseup'?: import("svelte/elements").MouseEventHandler<HTMLAudioElement> | null | undefined;
        'on:select'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:selectionchange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:selectstart'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:touchcancel'?: import("svelte/elements").TouchEventHandler<HTMLAudioElement> | null | undefined;
        'on:touchend'?: import("svelte/elements").TouchEventHandler<HTMLAudioElement> | null | undefined;
        'on:touchmove'?: import("svelte/elements").TouchEventHandler<HTMLAudioElement> | null | undefined;
        'on:touchstart'?: import("svelte/elements").TouchEventHandler<HTMLAudioElement> | null | undefined;
        'on:gotpointercapture'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointercancel'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerdown'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerenter'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerleave'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointermove'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerout'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerover'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:pointerup'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:lostpointercapture'?: import("svelte/elements").PointerEventHandler<HTMLAudioElement> | null | undefined;
        'on:gamepadconnected'?: import("svelte/elements").GamepadEventHandler<HTMLAudioElement> | null | undefined;
        'on:gamepaddisconnected'?: import("svelte/elements").GamepadEventHandler<HTMLAudioElement> | null | undefined;
        'on:scroll'?: import("svelte/elements").UIEventHandler<HTMLAudioElement> | null | undefined;
        'on:scrollend'?: import("svelte/elements").UIEventHandler<HTMLAudioElement> | null | undefined;
        'on:resize'?: import("svelte/elements").UIEventHandler<HTMLAudioElement> | null | undefined;
        'on:wheel'?: import("svelte/elements").WheelEventHandler<HTMLAudioElement> | null | undefined;
        'on:animationstart'?: import("svelte/elements").AnimationEventHandler<HTMLAudioElement> | null | undefined;
        'on:animationend'?: import("svelte/elements").AnimationEventHandler<HTMLAudioElement> | null | undefined;
        'on:animationiteration'?: import("svelte/elements").AnimationEventHandler<HTMLAudioElement> | null | undefined;
        'on:transitionstart'?: import("svelte/elements").TransitionEventHandler<HTMLAudioElement> | null | undefined;
        'on:transitionrun'?: import("svelte/elements").TransitionEventHandler<HTMLAudioElement> | null | undefined;
        'on:transitionend'?: import("svelte/elements").TransitionEventHandler<HTMLAudioElement> | null | undefined;
        'on:transitioncancel'?: import("svelte/elements").TransitionEventHandler<HTMLAudioElement> | null | undefined;
        'on:outrostart'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAudioElement> | null | undefined;
        'on:outroend'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAudioElement> | null | undefined;
        'on:introstart'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAudioElement> | null | undefined;
        'on:introend'?: import("svelte/elements").EventHandler<CustomEvent<null>, HTMLAudioElement> | null | undefined;
        'on:message'?: import("svelte/elements").MessageEventHandler<HTMLAudioElement> | null | undefined;
        'on:messageerror'?: import("svelte/elements").MessageEventHandler<HTMLAudioElement> | null | undefined;
        'on:visibilitychange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:cancel'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:close'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:fullscreenchange'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        'on:fullscreenerror'?: import("svelte/elements").EventHandler<Event, HTMLAudioElement> | null | undefined;
        "data-testid"?: string | undefined;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export type AudioProps = typeof __propDef.props;
export type AudioEvents = typeof __propDef.events;
export type AudioSlots = typeof __propDef.slots;
export default class Audio extends SvelteComponent<AudioProps, AudioEvents, AudioSlots> {
}
export {};
