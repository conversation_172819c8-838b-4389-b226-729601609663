/** @typedef {typeof __propDef.props}  ThumbDownDefaultProps */
/** @typedef {typeof __propDef.events}  ThumbDownDefaultEvents */
/** @typedef {typeof __propDef.slots}  ThumbDownDefaultSlots */
export default class ThumbDownDefault extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type ThumbDownDefaultProps = typeof __propDef.props;
export type ThumbDownDefaultEvents = typeof __propDef.events;
export type ThumbDownDefaultSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
