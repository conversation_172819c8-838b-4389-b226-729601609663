/** @typedef {typeof __propDef.props}  DownloadProps */
/** @typedef {typeof __propDef.events}  DownloadEvents */
/** @typedef {typeof __propDef.slots}  DownloadSlots */
export default class Download extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type DownloadProps = typeof __propDef.props;
export type DownloadEvents = typeof __propDef.events;
export type DownloadSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
