/** @typedef {typeof __propDef.props}  ThumbUpActiveProps */
/** @typedef {typeof __propDef.events}  ThumbUpActiveEvents */
/** @typedef {typeof __propDef.slots}  ThumbUpActiveSlots */
export default class ThumbUpActive extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type ThumbUpActiveProps = typeof __propDef.props;
export type ThumbUpActiveEvents = typeof __propDef.events;
export type ThumbUpActiveSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
