/** @typedef {typeof __propDef.props}  ThumbUpDefaultProps */
/** @typedef {typeof __propDef.events}  ThumbUpDefaultEvents */
/** @typedef {typeof __propDef.slots}  ThumbUpDefaultSlots */
export default class ThumbUpDefault extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type ThumbUpDefaultProps = typeof __propDef.props;
export type ThumbUpDefaultEvents = typeof __propDef.events;
export type ThumbUpDefaultSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
