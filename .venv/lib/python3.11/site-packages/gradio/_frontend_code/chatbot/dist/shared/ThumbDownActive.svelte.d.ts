/** @typedef {typeof __propDef.props}  ThumbDownActiveProps */
/** @typedef {typeof __propDef.events}  ThumbDownActiveEvents */
/** @typedef {typeof __propDef.slots}  ThumbDownActiveSlots */
export default class ThumbDownActive extends SvelteComponent<{
    [x: string]: never;
}, {
    [evt: string]: CustomEvent<any>;
}, {}> {
}
export type ThumbDownActiveProps = typeof __propDef.props;
export type ThumbDownActiveEvents = typeof __propDef.events;
export type ThumbDownActiveSlots = typeof __propDef.slots;
import { SvelteComponent } from "svelte";
declare const __propDef: {
    props: {
        [x: string]: never;
    };
    events: {
        [evt: string]: CustomEvent<any>;
    };
    slots: {};
};
export {};
