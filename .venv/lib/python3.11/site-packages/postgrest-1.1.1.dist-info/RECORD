postgrest-1.1.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-1.1.1.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
postgrest-1.1.1.dist-info/METADATA,sha256=6pfWfr19tzmq7cUgYavU9dZISlT4uBirH7_QJ66Zhq0,3458
postgrest-1.1.1.dist-info/RECORD,,
postgrest-1.1.1.dist-info/WHEEL,sha256=Nq82e9rUAnEjt98J6MlVmMCZb-t9cYE2Ir1kpBmnWfs,88
postgrest/__init__.py,sha256=S2uX6fv8lrTP-8ftR3S2KJwg7XrEsErO82RvGOp-wQw,1634
postgrest/__pycache__/__init__.cpython-311.pyc,,
postgrest/__pycache__/base_client.cpython-311.pyc,,
postgrest/__pycache__/base_request_builder.cpython-311.pyc,,
postgrest/__pycache__/constants.cpython-311.pyc,,
postgrest/__pycache__/exceptions.cpython-311.pyc,,
postgrest/__pycache__/types.cpython-311.pyc,,
postgrest/__pycache__/utils.cpython-311.pyc,,
postgrest/__pycache__/version.cpython-311.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-311.pyc,,
postgrest/_async/__pycache__/client.cpython-311.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-311.pyc,,
postgrest/_async/client.py,sha256=dh_ARwQmDBAsNTOiGjhimAEU_ENMda-qhBO914e-jr4,6154
postgrest/_async/request_builder.py,sha256=rmQN8_8FuuiJPSB7JnkyxGY49JQoZgQy0uWi-qDz7SU,14257
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-311.pyc,,
postgrest/_sync/__pycache__/client.cpython-311.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-311.pyc,,
postgrest/_sync/client.py,sha256=sT2IUcBsxjJ8E9yb6enL9uaDkH3P_dOzPerDz_2F4qM,6079
postgrest/_sync/request_builder.py,sha256=00m-xXMmCA1V3v5QEl-SUYLjUfHHU3aS6qdlU_neeAM,14148
postgrest/base_client.py,sha256=ArVMR5fCFSQw3Bmr6Y55eou7gEWKOF3SuD8e2Rlge2s,2242
postgrest/base_request_builder.py,sha256=g_hxFNoHd8HTyyilzmV8lGwHNujjG_eNH3OD-2vFhhg,23753
postgrest/constants.py,sha256=VZrlQtgGV-qwcjwqhlJOZBhPHzbSICjDJbabcAlPMUY,153
postgrest/exceptions.py,sha256=3aGUMf86WZSOTsZgtixkKS2JqPtdIQNMgOH23Flhb9o,1892
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=o1CCqNb7TAfdaCoYBF137kDio37dWszGNRaQJsDhHqY,986
postgrest/utils.py,sha256=-ew4s37_1B4DDZFV3hkjkRYzzjZSd0VlzRGLsNNaUAM,2141
postgrest/version.py,sha256=t3llS24kUViySoo41SSEP6GQNyjkpN5qLx7vs2qkyXI,52
