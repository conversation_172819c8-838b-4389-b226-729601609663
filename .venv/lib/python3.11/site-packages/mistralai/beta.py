"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from .basesdk import BaseSDK
from .sdkconfiguration import SDKConfiguration
from mistralai.conversations import Conversations
from mistralai.libraries import Libraries
from mistralai.mistral_agents import MistralAgents


class Beta(BaseSDK):
    conversations: Conversations
    r"""(beta) Conversations API"""
    agents: MistralAgents
    r"""(beta) Agents API"""
    libraries: Libraries
    r"""(beta) Libraries API for indexing documents to enhance agent capabilities."""

    def __init__(self, sdk_config: SDKConfiguration) -> None:
        BaseSDK.__init__(self, sdk_config)
        self.sdk_configuration = sdk_config
        self._init_sdks()

    def _init_sdks(self):
        self.conversations = Conversations(self.sdk_configuration)
        self.agents = MistralAgents(self.sdk_configuration)
        self.libraries = Libraries(self.sdk_configuration)
