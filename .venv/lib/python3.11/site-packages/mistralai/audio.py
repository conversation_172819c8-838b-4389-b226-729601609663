"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from .basesdk import BaseSDK
from .sdkconfiguration import SDKConfiguration
from mistralai.transcriptions import Transcriptions


class Audio(BaseSDK):
    transcriptions: Transcriptions
    r"""API for audio transcription."""

    def __init__(self, sdk_config: SDKConfiguration) -> None:
        BaseSDK.__init__(self, sdk_config)
        self.sdk_configuration = sdk_config
        self._init_sdks()

    def _init_sdks(self):
        self.transcriptions = Transcriptions(self.sdk_configuration)
