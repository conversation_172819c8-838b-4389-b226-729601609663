"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

import importlib.metadata

__title__: str = "mistralai"
__version__: str = "1.9.3"
__openapi_doc_version__: str = "1.0.0"
__gen_version__: str = "2.634.2"
__user_agent__: str = "speakeasy-sdk/python 1.9.3 2.634.2 1.0.0 mistralai"

try:
    if __package__ is not None:
        __version__ = importlib.metadata.version(__package__)
except importlib.metadata.PackageNotFoundError:
    pass
