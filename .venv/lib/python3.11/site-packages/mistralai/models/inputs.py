"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .assistantmessage import AssistantMessage, AssistantMessageTypedDict
from .instructrequest import InstructRequest, InstructRequestTypedDict
from .systemmessage import SystemMessage, SystemMessageTypedDict
from .toolmessage import ToolMessage, ToolMessageTypedDict
from .usermessage import UserMessage, UserMessageTypedDict
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import List, Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


InstructRequestInputsMessagesTypedDict = TypeAliasType(
    "InstructRequestInputsMessagesTypedDict",
    Union[
        SystemMessageTypedDict,
        UserMessageTypedDict,
        AssistantMessageTypedDict,
        ToolMessageTypedDict,
    ],
)


InstructRequestInputsMessages = Annotated[
    Union[
        Annotated[AssistantMessage, Tag("assistant")],
        Annotated[SystemMessage, Tag("system")],
        Annotated[ToolMessage, Tag("tool")],
        Annotated[UserMessage, Tag("user")],
    ],
    Discriminator(lambda m: get_discriminator(m, "role", "role")),
]


class InstructRequestInputsTypedDict(TypedDict):
    messages: List[InstructRequestInputsMessagesTypedDict]


class InstructRequestInputs(BaseModel):
    messages: List[InstructRequestInputsMessages]


InputsTypedDict = TypeAliasType(
    "InputsTypedDict",
    Union[InstructRequestInputsTypedDict, List[InstructRequestTypedDict]],
)
r"""Chat to classify"""


Inputs = TypeAliasType("Inputs", Union[InstructRequestInputs, List[InstructRequest]])
r"""Chat to classify"""
