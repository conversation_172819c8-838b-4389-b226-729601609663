"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ImageGenerationToolType = Literal["image_generation"]


class ImageGenerationToolTypedDict(TypedDict):
    type: NotRequired[ImageGenerationToolType]


class ImageGenerationTool(BaseModel):
    type: Optional[ImageGenerationToolType] = "image_generation"
