"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


AudioChunkType = Literal["input_audio"]


class AudioChunkTypedDict(TypedDict):
    input_audio: str
    type: NotRequired[AudioChunkType]


class AudioChunk(BaseModel):
    input_audio: str

    type: Optional[AudioChunkType] = "input_audio"
