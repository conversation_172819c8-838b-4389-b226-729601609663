"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from typing import TYPE_CHECKING
from importlib import import_module

if TYPE_CHECKING:
    from .agent import (
        Agent,
        AgentObject,
        AgentTools,
        AgentToolsTypedDict,
        AgentTypedDict,
    )
    from .agentconversation import (
        AgentConversation,
        AgentConversationObject,
        AgentConversationTypedDict,
    )
    from .agentcreationrequest import (
        AgentCreationRequest,
        AgentCreationRequestTools,
        AgentCreationRequestToolsTypedDict,
        AgentCreationRequestTypedDict,
    )
    from .agenthandoffdoneevent import (
        AgentHandoffDoneEvent,
        AgentHandoffDoneEventType,
        AgentHandoffDoneEventTypedDict,
    )
    from .agenthandoffentry import (
        AgentHandoffEntry,
        AgentHandoffEntryObject,
        AgentHandoffEntryType,
        AgentHandoffEntryTypedDict,
    )
    from .agenthandoffstartedevent import (
        AgentHandoffStartedEvent,
        AgentHandoffStartedEventType,
        AgentHandoffStartedEventTypedDict,
    )
    from .agents_api_v1_agents_getop import (
        AgentsAPIV1AgentsGetRequest,
        AgentsAPIV1AgentsGetRequestTypedDict,
    )
    from .agents_api_v1_agents_listop import (
        AgentsAPIV1AgentsListRequest,
        AgentsAPIV1AgentsListRequestTypedDict,
    )
    from .agents_api_v1_agents_update_versionop import (
        AgentsAPIV1AgentsUpdateVersionRequest,
        AgentsAPIV1AgentsUpdateVersionRequestTypedDict,
    )
    from .agents_api_v1_agents_updateop import (
        AgentsAPIV1AgentsUpdateRequest,
        AgentsAPIV1AgentsUpdateRequestTypedDict,
    )
    from .agents_api_v1_conversations_append_streamop import (
        AgentsAPIV1ConversationsAppendStreamRequest,
        AgentsAPIV1ConversationsAppendStreamRequestTypedDict,
    )
    from .agents_api_v1_conversations_appendop import (
        AgentsAPIV1ConversationsAppendRequest,
        AgentsAPIV1ConversationsAppendRequestTypedDict,
    )
    from .agents_api_v1_conversations_getop import (
        AgentsAPIV1ConversationsGetRequest,
        AgentsAPIV1ConversationsGetRequestTypedDict,
        AgentsAPIV1ConversationsGetResponseV1ConversationsGet,
        AgentsAPIV1ConversationsGetResponseV1ConversationsGetTypedDict,
    )
    from .agents_api_v1_conversations_historyop import (
        AgentsAPIV1ConversationsHistoryRequest,
        AgentsAPIV1ConversationsHistoryRequestTypedDict,
    )
    from .agents_api_v1_conversations_listop import (
        AgentsAPIV1ConversationsListRequest,
        AgentsAPIV1ConversationsListRequestTypedDict,
        ResponseBody,
        ResponseBodyTypedDict,
    )
    from .agents_api_v1_conversations_messagesop import (
        AgentsAPIV1ConversationsMessagesRequest,
        AgentsAPIV1ConversationsMessagesRequestTypedDict,
    )
    from .agents_api_v1_conversations_restart_streamop import (
        AgentsAPIV1ConversationsRestartStreamRequest,
        AgentsAPIV1ConversationsRestartStreamRequestTypedDict,
    )
    from .agents_api_v1_conversations_restartop import (
        AgentsAPIV1ConversationsRestartRequest,
        AgentsAPIV1ConversationsRestartRequestTypedDict,
    )
    from .agentscompletionrequest import (
        AgentsCompletionRequest,
        AgentsCompletionRequestMessages,
        AgentsCompletionRequestMessagesTypedDict,
        AgentsCompletionRequestStop,
        AgentsCompletionRequestStopTypedDict,
        AgentsCompletionRequestToolChoice,
        AgentsCompletionRequestToolChoiceTypedDict,
        AgentsCompletionRequestTypedDict,
    )
    from .agentscompletionstreamrequest import (
        AgentsCompletionStreamRequest,
        AgentsCompletionStreamRequestMessages,
        AgentsCompletionStreamRequestMessagesTypedDict,
        AgentsCompletionStreamRequestStop,
        AgentsCompletionStreamRequestStopTypedDict,
        AgentsCompletionStreamRequestToolChoice,
        AgentsCompletionStreamRequestToolChoiceTypedDict,
        AgentsCompletionStreamRequestTypedDict,
    )
    from .agentupdaterequest import (
        AgentUpdateRequest,
        AgentUpdateRequestTools,
        AgentUpdateRequestToolsTypedDict,
        AgentUpdateRequestTypedDict,
    )
    from .apiendpoint import APIEndpoint
    from .archiveftmodelout import (
        ArchiveFTModelOut,
        ArchiveFTModelOutObject,
        ArchiveFTModelOutTypedDict,
    )
    from .assistantmessage import (
        AssistantMessage,
        AssistantMessageContent,
        AssistantMessageContentTypedDict,
        AssistantMessageRole,
        AssistantMessageTypedDict,
    )
    from .audiochunk import AudioChunk, AudioChunkType, AudioChunkTypedDict
    from .audiotranscriptionrequest import (
        AudioTranscriptionRequest,
        AudioTranscriptionRequestTypedDict,
    )
    from .audiotranscriptionrequeststream import (
        AudioTranscriptionRequestStream,
        AudioTranscriptionRequestStreamTypedDict,
    )
    from .basemodelcard import BaseModelCard, BaseModelCardType, BaseModelCardTypedDict
    from .batcherror import BatchError, BatchErrorTypedDict
    from .batchjobin import BatchJobIn, BatchJobInTypedDict
    from .batchjobout import BatchJobOut, BatchJobOutObject, BatchJobOutTypedDict
    from .batchjobsout import BatchJobsOut, BatchJobsOutObject, BatchJobsOutTypedDict
    from .batchjobstatus import BatchJobStatus
    from .builtinconnectors import BuiltInConnectors
    from .chatclassificationrequest import (
        ChatClassificationRequest,
        ChatClassificationRequestTypedDict,
    )
    from .chatcompletionchoice import (
        ChatCompletionChoice,
        ChatCompletionChoiceTypedDict,
        FinishReason,
    )
    from .chatcompletionrequest import (
        ChatCompletionRequest,
        ChatCompletionRequestToolChoice,
        ChatCompletionRequestToolChoiceTypedDict,
        ChatCompletionRequestTypedDict,
        Messages,
        MessagesTypedDict,
        Stop,
        StopTypedDict,
    )
    from .chatcompletionresponse import (
        ChatCompletionResponse,
        ChatCompletionResponseTypedDict,
    )
    from .chatcompletionstreamrequest import (
        ChatCompletionStreamRequest,
        ChatCompletionStreamRequestMessages,
        ChatCompletionStreamRequestMessagesTypedDict,
        ChatCompletionStreamRequestStop,
        ChatCompletionStreamRequestStopTypedDict,
        ChatCompletionStreamRequestToolChoice,
        ChatCompletionStreamRequestToolChoiceTypedDict,
        ChatCompletionStreamRequestTypedDict,
    )
    from .chatmoderationrequest import (
        ChatModerationRequest,
        ChatModerationRequestInputs,
        ChatModerationRequestInputsTypedDict,
        ChatModerationRequestTypedDict,
        One,
        OneTypedDict,
        Two,
        TwoTypedDict,
    )
    from .checkpointout import CheckpointOut, CheckpointOutTypedDict
    from .classificationrequest import (
        ClassificationRequest,
        ClassificationRequestInputs,
        ClassificationRequestInputsTypedDict,
        ClassificationRequestTypedDict,
    )
    from .classificationresponse import (
        ClassificationResponse,
        ClassificationResponseTypedDict,
    )
    from .classificationtargetresult import (
        ClassificationTargetResult,
        ClassificationTargetResultTypedDict,
    )
    from .classifierdetailedjobout import (
        ClassifierDetailedJobOut,
        ClassifierDetailedJobOutIntegrations,
        ClassifierDetailedJobOutIntegrationsTypedDict,
        ClassifierDetailedJobOutJobType,
        ClassifierDetailedJobOutObject,
        ClassifierDetailedJobOutStatus,
        ClassifierDetailedJobOutTypedDict,
    )
    from .classifierftmodelout import (
        ClassifierFTModelOut,
        ClassifierFTModelOutModelType,
        ClassifierFTModelOutObject,
        ClassifierFTModelOutTypedDict,
    )
    from .classifierjobout import (
        ClassifierJobOut,
        ClassifierJobOutIntegrations,
        ClassifierJobOutIntegrationsTypedDict,
        ClassifierJobOutJobType,
        ClassifierJobOutObject,
        ClassifierJobOutStatus,
        ClassifierJobOutTypedDict,
    )
    from .classifiertargetin import ClassifierTargetIn, ClassifierTargetInTypedDict
    from .classifiertargetout import ClassifierTargetOut, ClassifierTargetOutTypedDict
    from .classifiertrainingparameters import (
        ClassifierTrainingParameters,
        ClassifierTrainingParametersTypedDict,
    )
    from .classifiertrainingparametersin import (
        ClassifierTrainingParametersIn,
        ClassifierTrainingParametersInTypedDict,
    )
    from .codeinterpretertool import (
        CodeInterpreterTool,
        CodeInterpreterToolType,
        CodeInterpreterToolTypedDict,
    )
    from .completionargs import CompletionArgs, CompletionArgsTypedDict
    from .completionargsstop import CompletionArgsStop, CompletionArgsStopTypedDict
    from .completionchunk import CompletionChunk, CompletionChunkTypedDict
    from .completiondetailedjobout import (
        CompletionDetailedJobOut,
        CompletionDetailedJobOutIntegrations,
        CompletionDetailedJobOutIntegrationsTypedDict,
        CompletionDetailedJobOutJobType,
        CompletionDetailedJobOutObject,
        CompletionDetailedJobOutRepositories,
        CompletionDetailedJobOutRepositoriesTypedDict,
        CompletionDetailedJobOutStatus,
        CompletionDetailedJobOutTypedDict,
    )
    from .completionevent import CompletionEvent, CompletionEventTypedDict
    from .completionftmodelout import (
        CompletionFTModelOut,
        CompletionFTModelOutObject,
        CompletionFTModelOutTypedDict,
        ModelType,
    )
    from .completionjobout import (
        CompletionJobOut,
        CompletionJobOutObject,
        CompletionJobOutTypedDict,
        Integrations,
        IntegrationsTypedDict,
        JobType,
        Repositories,
        RepositoriesTypedDict,
        Status,
    )
    from .completionresponsestreamchoice import (
        CompletionResponseStreamChoice,
        CompletionResponseStreamChoiceFinishReason,
        CompletionResponseStreamChoiceTypedDict,
    )
    from .completiontrainingparameters import (
        CompletionTrainingParameters,
        CompletionTrainingParametersTypedDict,
    )
    from .completiontrainingparametersin import (
        CompletionTrainingParametersIn,
        CompletionTrainingParametersInTypedDict,
    )
    from .contentchunk import ContentChunk, ContentChunkTypedDict
    from .conversationappendrequest import (
        ConversationAppendRequest,
        ConversationAppendRequestHandoffExecution,
        ConversationAppendRequestTypedDict,
    )
    from .conversationappendstreamrequest import (
        ConversationAppendStreamRequest,
        ConversationAppendStreamRequestHandoffExecution,
        ConversationAppendStreamRequestTypedDict,
    )
    from .conversationevents import (
        ConversationEvents,
        ConversationEventsData,
        ConversationEventsDataTypedDict,
        ConversationEventsTypedDict,
    )
    from .conversationhistory import (
        ConversationHistory,
        ConversationHistoryObject,
        ConversationHistoryTypedDict,
        Entries,
        EntriesTypedDict,
    )
    from .conversationinputs import ConversationInputs, ConversationInputsTypedDict
    from .conversationmessages import (
        ConversationMessages,
        ConversationMessagesObject,
        ConversationMessagesTypedDict,
    )
    from .conversationrequest import (
        ConversationRequest,
        ConversationRequestTypedDict,
        HandoffExecution,
        Tools,
        ToolsTypedDict,
    )
    from .conversationresponse import (
        ConversationResponse,
        ConversationResponseObject,
        ConversationResponseTypedDict,
        Outputs,
        OutputsTypedDict,
    )
    from .conversationrestartrequest import (
        ConversationRestartRequest,
        ConversationRestartRequestHandoffExecution,
        ConversationRestartRequestTypedDict,
    )
    from .conversationrestartstreamrequest import (
        ConversationRestartStreamRequest,
        ConversationRestartStreamRequestHandoffExecution,
        ConversationRestartStreamRequestTypedDict,
    )
    from .conversationstreamrequest import (
        ConversationStreamRequest,
        ConversationStreamRequestHandoffExecution,
        ConversationStreamRequestTools,
        ConversationStreamRequestToolsTypedDict,
        ConversationStreamRequestTypedDict,
    )
    from .conversationusageinfo import (
        ConversationUsageInfo,
        ConversationUsageInfoTypedDict,
    )
    from .delete_model_v1_models_model_id_deleteop import (
        DeleteModelV1ModelsModelIDDeleteRequest,
        DeleteModelV1ModelsModelIDDeleteRequestTypedDict,
    )
    from .deletefileout import DeleteFileOut, DeleteFileOutTypedDict
    from .deletemodelout import DeleteModelOut, DeleteModelOutTypedDict
    from .deltamessage import (
        Content,
        ContentTypedDict,
        DeltaMessage,
        DeltaMessageTypedDict,
    )
    from .documentlibrarytool import (
        DocumentLibraryTool,
        DocumentLibraryToolType,
        DocumentLibraryToolTypedDict,
    )
    from .documentout import DocumentOut, DocumentOutTypedDict
    from .documenttextcontent import DocumentTextContent, DocumentTextContentTypedDict
    from .documentupdatein import DocumentUpdateIn, DocumentUpdateInTypedDict
    from .documenturlchunk import (
        DocumentURLChunk,
        DocumentURLChunkType,
        DocumentURLChunkTypedDict,
    )
    from .embeddingdtype import EmbeddingDtype
    from .embeddingrequest import (
        EmbeddingRequest,
        EmbeddingRequestInputs,
        EmbeddingRequestInputsTypedDict,
        EmbeddingRequestTypedDict,
    )
    from .embeddingresponse import EmbeddingResponse, EmbeddingResponseTypedDict
    from .embeddingresponsedata import (
        EmbeddingResponseData,
        EmbeddingResponseDataTypedDict,
    )
    from .entitytype import EntityType
    from .eventout import EventOut, EventOutTypedDict
    from .file import File, FileTypedDict
    from .filechunk import FileChunk, FileChunkTypedDict
    from .filepurpose import FilePurpose
    from .files_api_routes_delete_fileop import (
        FilesAPIRoutesDeleteFileRequest,
        FilesAPIRoutesDeleteFileRequestTypedDict,
    )
    from .files_api_routes_download_fileop import (
        FilesAPIRoutesDownloadFileRequest,
        FilesAPIRoutesDownloadFileRequestTypedDict,
    )
    from .files_api_routes_get_signed_urlop import (
        FilesAPIRoutesGetSignedURLRequest,
        FilesAPIRoutesGetSignedURLRequestTypedDict,
    )
    from .files_api_routes_list_filesop import (
        FilesAPIRoutesListFilesRequest,
        FilesAPIRoutesListFilesRequestTypedDict,
    )
    from .files_api_routes_retrieve_fileop import (
        FilesAPIRoutesRetrieveFileRequest,
        FilesAPIRoutesRetrieveFileRequestTypedDict,
    )
    from .files_api_routes_upload_fileop import (
        FilesAPIRoutesUploadFileMultiPartBodyParams,
        FilesAPIRoutesUploadFileMultiPartBodyParamsTypedDict,
    )
    from .fileschema import FileSchema, FileSchemaTypedDict
    from .filesignedurl import FileSignedURL, FileSignedURLTypedDict
    from .fimcompletionrequest import (
        FIMCompletionRequest,
        FIMCompletionRequestStop,
        FIMCompletionRequestStopTypedDict,
        FIMCompletionRequestTypedDict,
    )
    from .fimcompletionresponse import (
        FIMCompletionResponse,
        FIMCompletionResponseTypedDict,
    )
    from .fimcompletionstreamrequest import (
        FIMCompletionStreamRequest,
        FIMCompletionStreamRequestStop,
        FIMCompletionStreamRequestStopTypedDict,
        FIMCompletionStreamRequestTypedDict,
    )
    from .finetuneablemodeltype import FineTuneableModelType
    from .ftclassifierlossfunction import FTClassifierLossFunction
    from .ftmodelcapabilitiesout import (
        FTModelCapabilitiesOut,
        FTModelCapabilitiesOutTypedDict,
    )
    from .ftmodelcard import FTModelCard, FTModelCardType, FTModelCardTypedDict
    from .function import Function, FunctionTypedDict
    from .functioncall import (
        Arguments,
        ArgumentsTypedDict,
        FunctionCall,
        FunctionCallTypedDict,
    )
    from .functioncallentry import (
        FunctionCallEntry,
        FunctionCallEntryObject,
        FunctionCallEntryType,
        FunctionCallEntryTypedDict,
    )
    from .functioncallentryarguments import (
        FunctionCallEntryArguments,
        FunctionCallEntryArgumentsTypedDict,
    )
    from .functioncallevent import (
        FunctionCallEvent,
        FunctionCallEventType,
        FunctionCallEventTypedDict,
    )
    from .functionname import FunctionName, FunctionNameTypedDict
    from .functionresultentry import (
        FunctionResultEntry,
        FunctionResultEntryObject,
        FunctionResultEntryType,
        FunctionResultEntryTypedDict,
    )
    from .functiontool import FunctionTool, FunctionToolType, FunctionToolTypedDict
    from .githubrepositoryin import (
        GithubRepositoryIn,
        GithubRepositoryInType,
        GithubRepositoryInTypedDict,
    )
    from .githubrepositoryout import (
        GithubRepositoryOut,
        GithubRepositoryOutType,
        GithubRepositoryOutTypedDict,
    )
    from .httpvalidationerror import HTTPValidationError, HTTPValidationErrorData
    from .imagegenerationtool import (
        ImageGenerationTool,
        ImageGenerationToolType,
        ImageGenerationToolTypedDict,
    )
    from .imageurl import ImageURL, ImageURLTypedDict
    from .imageurlchunk import (
        ImageURLChunk,
        ImageURLChunkImageURL,
        ImageURLChunkImageURLTypedDict,
        ImageURLChunkType,
        ImageURLChunkTypedDict,
    )
    from .inputentries import InputEntries, InputEntriesTypedDict
    from .inputs import (
        Inputs,
        InputsTypedDict,
        InstructRequestInputs,
        InstructRequestInputsMessages,
        InstructRequestInputsMessagesTypedDict,
        InstructRequestInputsTypedDict,
    )
    from .instructrequest import (
        InstructRequest,
        InstructRequestMessages,
        InstructRequestMessagesTypedDict,
        InstructRequestTypedDict,
    )
    from .jobin import (
        Hyperparameters,
        HyperparametersTypedDict,
        JobIn,
        JobInIntegrations,
        JobInIntegrationsTypedDict,
        JobInRepositories,
        JobInRepositoriesTypedDict,
        JobInTypedDict,
    )
    from .jobmetadataout import JobMetadataOut, JobMetadataOutTypedDict
    from .jobs_api_routes_batch_cancel_batch_jobop import (
        JobsAPIRoutesBatchCancelBatchJobRequest,
        JobsAPIRoutesBatchCancelBatchJobRequestTypedDict,
    )
    from .jobs_api_routes_batch_get_batch_jobop import (
        JobsAPIRoutesBatchGetBatchJobRequest,
        JobsAPIRoutesBatchGetBatchJobRequestTypedDict,
    )
    from .jobs_api_routes_batch_get_batch_jobsop import (
        JobsAPIRoutesBatchGetBatchJobsRequest,
        JobsAPIRoutesBatchGetBatchJobsRequestTypedDict,
    )
    from .jobs_api_routes_fine_tuning_archive_fine_tuned_modelop import (
        JobsAPIRoutesFineTuningArchiveFineTunedModelRequest,
        JobsAPIRoutesFineTuningArchiveFineTunedModelRequestTypedDict,
    )
    from .jobs_api_routes_fine_tuning_cancel_fine_tuning_jobop import (
        JobsAPIRoutesFineTuningCancelFineTuningJobRequest,
        JobsAPIRoutesFineTuningCancelFineTuningJobRequestTypedDict,
        JobsAPIRoutesFineTuningCancelFineTuningJobResponse,
        JobsAPIRoutesFineTuningCancelFineTuningJobResponseTypedDict,
    )
    from .jobs_api_routes_fine_tuning_create_fine_tuning_jobop import (
        JobsAPIRoutesFineTuningCreateFineTuningJobResponse,
        JobsAPIRoutesFineTuningCreateFineTuningJobResponseTypedDict,
        Response1,
        Response1TypedDict,
    )
    from .jobs_api_routes_fine_tuning_get_fine_tuning_jobop import (
        JobsAPIRoutesFineTuningGetFineTuningJobRequest,
        JobsAPIRoutesFineTuningGetFineTuningJobRequestTypedDict,
        JobsAPIRoutesFineTuningGetFineTuningJobResponse,
        JobsAPIRoutesFineTuningGetFineTuningJobResponseTypedDict,
    )
    from .jobs_api_routes_fine_tuning_get_fine_tuning_jobsop import (
        JobsAPIRoutesFineTuningGetFineTuningJobsRequest,
        JobsAPIRoutesFineTuningGetFineTuningJobsRequestTypedDict,
        QueryParamStatus,
    )
    from .jobs_api_routes_fine_tuning_start_fine_tuning_jobop import (
        JobsAPIRoutesFineTuningStartFineTuningJobRequest,
        JobsAPIRoutesFineTuningStartFineTuningJobRequestTypedDict,
        JobsAPIRoutesFineTuningStartFineTuningJobResponse,
        JobsAPIRoutesFineTuningStartFineTuningJobResponseTypedDict,
    )
    from .jobs_api_routes_fine_tuning_unarchive_fine_tuned_modelop import (
        JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequest,
        JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequestTypedDict,
    )
    from .jobs_api_routes_fine_tuning_update_fine_tuned_modelop import (
        JobsAPIRoutesFineTuningUpdateFineTunedModelRequest,
        JobsAPIRoutesFineTuningUpdateFineTunedModelRequestTypedDict,
        JobsAPIRoutesFineTuningUpdateFineTunedModelResponse,
        JobsAPIRoutesFineTuningUpdateFineTunedModelResponseTypedDict,
    )
    from .jobsout import (
        JobsOut,
        JobsOutData,
        JobsOutDataTypedDict,
        JobsOutObject,
        JobsOutTypedDict,
    )
    from .jsonschema import JSONSchema, JSONSchemaTypedDict
    from .legacyjobmetadataout import (
        LegacyJobMetadataOut,
        LegacyJobMetadataOutObject,
        LegacyJobMetadataOutTypedDict,
    )
    from .libraries_delete_v1op import (
        LibrariesDeleteV1Request,
        LibrariesDeleteV1RequestTypedDict,
    )
    from .libraries_documents_delete_v1op import (
        LibrariesDocumentsDeleteV1Request,
        LibrariesDocumentsDeleteV1RequestTypedDict,
    )
    from .libraries_documents_get_extracted_text_signed_url_v1op import (
        LibrariesDocumentsGetExtractedTextSignedURLV1Request,
        LibrariesDocumentsGetExtractedTextSignedURLV1RequestTypedDict,
    )
    from .libraries_documents_get_signed_url_v1op import (
        LibrariesDocumentsGetSignedURLV1Request,
        LibrariesDocumentsGetSignedURLV1RequestTypedDict,
    )
    from .libraries_documents_get_status_v1op import (
        LibrariesDocumentsGetStatusV1Request,
        LibrariesDocumentsGetStatusV1RequestTypedDict,
    )
    from .libraries_documents_get_text_content_v1op import (
        LibrariesDocumentsGetTextContentV1Request,
        LibrariesDocumentsGetTextContentV1RequestTypedDict,
    )
    from .libraries_documents_get_v1op import (
        LibrariesDocumentsGetV1Request,
        LibrariesDocumentsGetV1RequestTypedDict,
    )
    from .libraries_documents_list_v1op import (
        LibrariesDocumentsListV1Request,
        LibrariesDocumentsListV1RequestTypedDict,
    )
    from .libraries_documents_reprocess_v1op import (
        LibrariesDocumentsReprocessV1Request,
        LibrariesDocumentsReprocessV1RequestTypedDict,
    )
    from .libraries_documents_update_v1op import (
        LibrariesDocumentsUpdateV1Request,
        LibrariesDocumentsUpdateV1RequestTypedDict,
    )
    from .libraries_documents_upload_v1op import (
        LibrariesDocumentsUploadV1DocumentUpload,
        LibrariesDocumentsUploadV1DocumentUploadTypedDict,
        LibrariesDocumentsUploadV1Request,
        LibrariesDocumentsUploadV1RequestTypedDict,
    )
    from .libraries_get_v1op import (
        LibrariesGetV1Request,
        LibrariesGetV1RequestTypedDict,
    )
    from .libraries_share_create_v1op import (
        LibrariesShareCreateV1Request,
        LibrariesShareCreateV1RequestTypedDict,
    )
    from .libraries_share_delete_v1op import (
        LibrariesShareDeleteV1Request,
        LibrariesShareDeleteV1RequestTypedDict,
    )
    from .libraries_share_list_v1op import (
        LibrariesShareListV1Request,
        LibrariesShareListV1RequestTypedDict,
    )
    from .libraries_update_v1op import (
        LibrariesUpdateV1Request,
        LibrariesUpdateV1RequestTypedDict,
    )
    from .libraryin import LibraryIn, LibraryInTypedDict
    from .libraryinupdate import LibraryInUpdate, LibraryInUpdateTypedDict
    from .libraryout import LibraryOut, LibraryOutTypedDict
    from .listdocumentout import ListDocumentOut, ListDocumentOutTypedDict
    from .listfilesout import ListFilesOut, ListFilesOutTypedDict
    from .listlibraryout import ListLibraryOut, ListLibraryOutTypedDict
    from .listsharingout import ListSharingOut, ListSharingOutTypedDict
    from .messageentries import MessageEntries, MessageEntriesTypedDict
    from .messageinputcontentchunks import (
        MessageInputContentChunks,
        MessageInputContentChunksTypedDict,
    )
    from .messageinputentry import (
        MessageInputEntry,
        MessageInputEntryContent,
        MessageInputEntryContentTypedDict,
        MessageInputEntryRole,
        MessageInputEntryType,
        MessageInputEntryTypedDict,
        Object,
    )
    from .messageoutputcontentchunks import (
        MessageOutputContentChunks,
        MessageOutputContentChunksTypedDict,
    )
    from .messageoutputentry import (
        MessageOutputEntry,
        MessageOutputEntryContent,
        MessageOutputEntryContentTypedDict,
        MessageOutputEntryObject,
        MessageOutputEntryRole,
        MessageOutputEntryType,
        MessageOutputEntryTypedDict,
    )
    from .messageoutputevent import (
        MessageOutputEvent,
        MessageOutputEventContent,
        MessageOutputEventContentTypedDict,
        MessageOutputEventRole,
        MessageOutputEventType,
        MessageOutputEventTypedDict,
    )
    from .metricout import MetricOut, MetricOutTypedDict
    from .mistralpromptmode import MistralPromptMode
    from .modelcapabilities import ModelCapabilities, ModelCapabilitiesTypedDict
    from .modelconversation import (
        ModelConversation,
        ModelConversationObject,
        ModelConversationTools,
        ModelConversationToolsTypedDict,
        ModelConversationTypedDict,
    )
    from .modellist import Data, DataTypedDict, ModelList, ModelListTypedDict
    from .moderationobject import ModerationObject, ModerationObjectTypedDict
    from .moderationresponse import ModerationResponse, ModerationResponseTypedDict
    from .ocrimageobject import OCRImageObject, OCRImageObjectTypedDict
    from .ocrpagedimensions import OCRPageDimensions, OCRPageDimensionsTypedDict
    from .ocrpageobject import OCRPageObject, OCRPageObjectTypedDict
    from .ocrrequest import Document, DocumentTypedDict, OCRRequest, OCRRequestTypedDict
    from .ocrresponse import OCRResponse, OCRResponseTypedDict
    from .ocrusageinfo import OCRUsageInfo, OCRUsageInfoTypedDict
    from .outputcontentchunks import OutputContentChunks, OutputContentChunksTypedDict
    from .paginationinfo import PaginationInfo, PaginationInfoTypedDict
    from .prediction import Prediction, PredictionTypedDict
    from .processingstatusout import ProcessingStatusOut, ProcessingStatusOutTypedDict
    from .referencechunk import (
        ReferenceChunk,
        ReferenceChunkType,
        ReferenceChunkTypedDict,
    )
    from .responsedoneevent import (
        ResponseDoneEvent,
        ResponseDoneEventType,
        ResponseDoneEventTypedDict,
    )
    from .responseerrorevent import (
        ResponseErrorEvent,
        ResponseErrorEventType,
        ResponseErrorEventTypedDict,
    )
    from .responseformat import ResponseFormat, ResponseFormatTypedDict
    from .responseformats import ResponseFormats
    from .responsestartedevent import (
        ResponseStartedEvent,
        ResponseStartedEventType,
        ResponseStartedEventTypedDict,
    )
    from .retrieve_model_v1_models_model_id_getop import (
        RetrieveModelV1ModelsModelIDGetRequest,
        RetrieveModelV1ModelsModelIDGetRequestTypedDict,
        RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGet,
        RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGetTypedDict,
    )
    from .retrievefileout import RetrieveFileOut, RetrieveFileOutTypedDict
    from .sampletype import SampleType
    from .sdkerror import SDKError
    from .security import Security, SecurityTypedDict
    from .shareenum import ShareEnum
    from .sharingdelete import SharingDelete, SharingDeleteTypedDict
    from .sharingin import SharingIn, SharingInTypedDict
    from .sharingout import SharingOut, SharingOutTypedDict
    from .source import Source
    from .ssetypes import SSETypes
    from .systemmessage import (
        Role,
        SystemMessage,
        SystemMessageContent,
        SystemMessageContentTypedDict,
        SystemMessageTypedDict,
    )
    from .textchunk import TextChunk, TextChunkType, TextChunkTypedDict
    from .thinkchunk import (
        ThinkChunk,
        ThinkChunkType,
        ThinkChunkTypedDict,
        Thinking,
        ThinkingTypedDict,
    )
    from .timestampgranularity import TimestampGranularity
    from .tool import Tool, ToolTypedDict
    from .toolcall import Metadata, MetadataTypedDict, ToolCall, ToolCallTypedDict
    from .toolchoice import ToolChoice, ToolChoiceTypedDict
    from .toolchoiceenum import ToolChoiceEnum
    from .toolexecutiondeltaevent import (
        ToolExecutionDeltaEvent,
        ToolExecutionDeltaEventType,
        ToolExecutionDeltaEventTypedDict,
    )
    from .toolexecutiondoneevent import (
        ToolExecutionDoneEvent,
        ToolExecutionDoneEventType,
        ToolExecutionDoneEventTypedDict,
    )
    from .toolexecutionentry import (
        ToolExecutionEntry,
        ToolExecutionEntryObject,
        ToolExecutionEntryType,
        ToolExecutionEntryTypedDict,
    )
    from .toolexecutionstartedevent import (
        ToolExecutionStartedEvent,
        ToolExecutionStartedEventType,
        ToolExecutionStartedEventTypedDict,
    )
    from .toolfilechunk import ToolFileChunk, ToolFileChunkType, ToolFileChunkTypedDict
    from .toolmessage import (
        ToolMessage,
        ToolMessageContent,
        ToolMessageContentTypedDict,
        ToolMessageRole,
        ToolMessageTypedDict,
    )
    from .toolreferencechunk import (
        ToolReferenceChunk,
        ToolReferenceChunkType,
        ToolReferenceChunkTypedDict,
    )
    from .tooltypes import ToolTypes
    from .trainingfile import TrainingFile, TrainingFileTypedDict
    from .transcriptionresponse import (
        TranscriptionResponse,
        TranscriptionResponseTypedDict,
    )
    from .transcriptionsegmentchunk import (
        TranscriptionSegmentChunk,
        TranscriptionSegmentChunkTypedDict,
        Type,
    )
    from .transcriptionstreamdone import (
        TranscriptionStreamDone,
        TranscriptionStreamDoneType,
        TranscriptionStreamDoneTypedDict,
    )
    from .transcriptionstreamevents import (
        TranscriptionStreamEvents,
        TranscriptionStreamEventsData,
        TranscriptionStreamEventsDataTypedDict,
        TranscriptionStreamEventsTypedDict,
    )
    from .transcriptionstreameventtypes import TranscriptionStreamEventTypes
    from .transcriptionstreamlanguage import (
        TranscriptionStreamLanguage,
        TranscriptionStreamLanguageType,
        TranscriptionStreamLanguageTypedDict,
    )
    from .transcriptionstreamsegmentdelta import (
        TranscriptionStreamSegmentDelta,
        TranscriptionStreamSegmentDeltaType,
        TranscriptionStreamSegmentDeltaTypedDict,
    )
    from .transcriptionstreamtextdelta import (
        TranscriptionStreamTextDelta,
        TranscriptionStreamTextDeltaType,
        TranscriptionStreamTextDeltaTypedDict,
    )
    from .unarchiveftmodelout import (
        UnarchiveFTModelOut,
        UnarchiveFTModelOutObject,
        UnarchiveFTModelOutTypedDict,
    )
    from .updateftmodelin import UpdateFTModelIn, UpdateFTModelInTypedDict
    from .uploadfileout import UploadFileOut, UploadFileOutTypedDict
    from .usageinfo import UsageInfo, UsageInfoTypedDict
    from .usermessage import (
        UserMessage,
        UserMessageContent,
        UserMessageContentTypedDict,
        UserMessageRole,
        UserMessageTypedDict,
    )
    from .validationerror import (
        Loc,
        LocTypedDict,
        ValidationError,
        ValidationErrorTypedDict,
    )
    from .wandbintegration import (
        WandbIntegration,
        WandbIntegrationType,
        WandbIntegrationTypedDict,
    )
    from .wandbintegrationout import (
        WandbIntegrationOut,
        WandbIntegrationOutType,
        WandbIntegrationOutTypedDict,
    )
    from .websearchpremiumtool import (
        WebSearchPremiumTool,
        WebSearchPremiumToolType,
        WebSearchPremiumToolTypedDict,
    )
    from .websearchtool import WebSearchTool, WebSearchToolType, WebSearchToolTypedDict

__all__ = [
    "APIEndpoint",
    "Agent",
    "AgentConversation",
    "AgentConversationObject",
    "AgentConversationTypedDict",
    "AgentCreationRequest",
    "AgentCreationRequestTools",
    "AgentCreationRequestToolsTypedDict",
    "AgentCreationRequestTypedDict",
    "AgentHandoffDoneEvent",
    "AgentHandoffDoneEventType",
    "AgentHandoffDoneEventTypedDict",
    "AgentHandoffEntry",
    "AgentHandoffEntryObject",
    "AgentHandoffEntryType",
    "AgentHandoffEntryTypedDict",
    "AgentHandoffStartedEvent",
    "AgentHandoffStartedEventType",
    "AgentHandoffStartedEventTypedDict",
    "AgentObject",
    "AgentTools",
    "AgentToolsTypedDict",
    "AgentTypedDict",
    "AgentUpdateRequest",
    "AgentUpdateRequestTools",
    "AgentUpdateRequestToolsTypedDict",
    "AgentUpdateRequestTypedDict",
    "AgentsAPIV1AgentsGetRequest",
    "AgentsAPIV1AgentsGetRequestTypedDict",
    "AgentsAPIV1AgentsListRequest",
    "AgentsAPIV1AgentsListRequestTypedDict",
    "AgentsAPIV1AgentsUpdateRequest",
    "AgentsAPIV1AgentsUpdateRequestTypedDict",
    "AgentsAPIV1AgentsUpdateVersionRequest",
    "AgentsAPIV1AgentsUpdateVersionRequestTypedDict",
    "AgentsAPIV1ConversationsAppendRequest",
    "AgentsAPIV1ConversationsAppendRequestTypedDict",
    "AgentsAPIV1ConversationsAppendStreamRequest",
    "AgentsAPIV1ConversationsAppendStreamRequestTypedDict",
    "AgentsAPIV1ConversationsGetRequest",
    "AgentsAPIV1ConversationsGetRequestTypedDict",
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGet",
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGetTypedDict",
    "AgentsAPIV1ConversationsHistoryRequest",
    "AgentsAPIV1ConversationsHistoryRequestTypedDict",
    "AgentsAPIV1ConversationsListRequest",
    "AgentsAPIV1ConversationsListRequestTypedDict",
    "AgentsAPIV1ConversationsMessagesRequest",
    "AgentsAPIV1ConversationsMessagesRequestTypedDict",
    "AgentsAPIV1ConversationsRestartRequest",
    "AgentsAPIV1ConversationsRestartRequestTypedDict",
    "AgentsAPIV1ConversationsRestartStreamRequest",
    "AgentsAPIV1ConversationsRestartStreamRequestTypedDict",
    "AgentsCompletionRequest",
    "AgentsCompletionRequestMessages",
    "AgentsCompletionRequestMessagesTypedDict",
    "AgentsCompletionRequestStop",
    "AgentsCompletionRequestStopTypedDict",
    "AgentsCompletionRequestToolChoice",
    "AgentsCompletionRequestToolChoiceTypedDict",
    "AgentsCompletionRequestTypedDict",
    "AgentsCompletionStreamRequest",
    "AgentsCompletionStreamRequestMessages",
    "AgentsCompletionStreamRequestMessagesTypedDict",
    "AgentsCompletionStreamRequestStop",
    "AgentsCompletionStreamRequestStopTypedDict",
    "AgentsCompletionStreamRequestToolChoice",
    "AgentsCompletionStreamRequestToolChoiceTypedDict",
    "AgentsCompletionStreamRequestTypedDict",
    "ArchiveFTModelOut",
    "ArchiveFTModelOutObject",
    "ArchiveFTModelOutTypedDict",
    "Arguments",
    "ArgumentsTypedDict",
    "AssistantMessage",
    "AssistantMessageContent",
    "AssistantMessageContentTypedDict",
    "AssistantMessageRole",
    "AssistantMessageTypedDict",
    "AudioChunk",
    "AudioChunkType",
    "AudioChunkTypedDict",
    "AudioTranscriptionRequest",
    "AudioTranscriptionRequestStream",
    "AudioTranscriptionRequestStreamTypedDict",
    "AudioTranscriptionRequestTypedDict",
    "BaseModelCard",
    "BaseModelCardType",
    "BaseModelCardTypedDict",
    "BatchError",
    "BatchErrorTypedDict",
    "BatchJobIn",
    "BatchJobInTypedDict",
    "BatchJobOut",
    "BatchJobOutObject",
    "BatchJobOutTypedDict",
    "BatchJobStatus",
    "BatchJobsOut",
    "BatchJobsOutObject",
    "BatchJobsOutTypedDict",
    "BuiltInConnectors",
    "ChatClassificationRequest",
    "ChatClassificationRequestTypedDict",
    "ChatCompletionChoice",
    "ChatCompletionChoiceTypedDict",
    "ChatCompletionRequest",
    "ChatCompletionRequestToolChoice",
    "ChatCompletionRequestToolChoiceTypedDict",
    "ChatCompletionRequestTypedDict",
    "ChatCompletionResponse",
    "ChatCompletionResponseTypedDict",
    "ChatCompletionStreamRequest",
    "ChatCompletionStreamRequestMessages",
    "ChatCompletionStreamRequestMessagesTypedDict",
    "ChatCompletionStreamRequestStop",
    "ChatCompletionStreamRequestStopTypedDict",
    "ChatCompletionStreamRequestToolChoice",
    "ChatCompletionStreamRequestToolChoiceTypedDict",
    "ChatCompletionStreamRequestTypedDict",
    "ChatModerationRequest",
    "ChatModerationRequestInputs",
    "ChatModerationRequestInputsTypedDict",
    "ChatModerationRequestTypedDict",
    "CheckpointOut",
    "CheckpointOutTypedDict",
    "ClassificationRequest",
    "ClassificationRequestInputs",
    "ClassificationRequestInputsTypedDict",
    "ClassificationRequestTypedDict",
    "ClassificationResponse",
    "ClassificationResponseTypedDict",
    "ClassificationTargetResult",
    "ClassificationTargetResultTypedDict",
    "ClassifierDetailedJobOut",
    "ClassifierDetailedJobOutIntegrations",
    "ClassifierDetailedJobOutIntegrationsTypedDict",
    "ClassifierDetailedJobOutJobType",
    "ClassifierDetailedJobOutObject",
    "ClassifierDetailedJobOutStatus",
    "ClassifierDetailedJobOutTypedDict",
    "ClassifierFTModelOut",
    "ClassifierFTModelOutModelType",
    "ClassifierFTModelOutObject",
    "ClassifierFTModelOutTypedDict",
    "ClassifierJobOut",
    "ClassifierJobOutIntegrations",
    "ClassifierJobOutIntegrationsTypedDict",
    "ClassifierJobOutJobType",
    "ClassifierJobOutObject",
    "ClassifierJobOutStatus",
    "ClassifierJobOutTypedDict",
    "ClassifierTargetIn",
    "ClassifierTargetInTypedDict",
    "ClassifierTargetOut",
    "ClassifierTargetOutTypedDict",
    "ClassifierTrainingParameters",
    "ClassifierTrainingParametersIn",
    "ClassifierTrainingParametersInTypedDict",
    "ClassifierTrainingParametersTypedDict",
    "CodeInterpreterTool",
    "CodeInterpreterToolType",
    "CodeInterpreterToolTypedDict",
    "CompletionArgs",
    "CompletionArgsStop",
    "CompletionArgsStopTypedDict",
    "CompletionArgsTypedDict",
    "CompletionChunk",
    "CompletionChunkTypedDict",
    "CompletionDetailedJobOut",
    "CompletionDetailedJobOutIntegrations",
    "CompletionDetailedJobOutIntegrationsTypedDict",
    "CompletionDetailedJobOutJobType",
    "CompletionDetailedJobOutObject",
    "CompletionDetailedJobOutRepositories",
    "CompletionDetailedJobOutRepositoriesTypedDict",
    "CompletionDetailedJobOutStatus",
    "CompletionDetailedJobOutTypedDict",
    "CompletionEvent",
    "CompletionEventTypedDict",
    "CompletionFTModelOut",
    "CompletionFTModelOutObject",
    "CompletionFTModelOutTypedDict",
    "CompletionJobOut",
    "CompletionJobOutObject",
    "CompletionJobOutTypedDict",
    "CompletionResponseStreamChoice",
    "CompletionResponseStreamChoiceFinishReason",
    "CompletionResponseStreamChoiceTypedDict",
    "CompletionTrainingParameters",
    "CompletionTrainingParametersIn",
    "CompletionTrainingParametersInTypedDict",
    "CompletionTrainingParametersTypedDict",
    "Content",
    "ContentChunk",
    "ContentChunkTypedDict",
    "ContentTypedDict",
    "ConversationAppendRequest",
    "ConversationAppendRequestHandoffExecution",
    "ConversationAppendRequestTypedDict",
    "ConversationAppendStreamRequest",
    "ConversationAppendStreamRequestHandoffExecution",
    "ConversationAppendStreamRequestTypedDict",
    "ConversationEvents",
    "ConversationEventsData",
    "ConversationEventsDataTypedDict",
    "ConversationEventsTypedDict",
    "ConversationHistory",
    "ConversationHistoryObject",
    "ConversationHistoryTypedDict",
    "ConversationInputs",
    "ConversationInputsTypedDict",
    "ConversationMessages",
    "ConversationMessagesObject",
    "ConversationMessagesTypedDict",
    "ConversationRequest",
    "ConversationRequestTypedDict",
    "ConversationResponse",
    "ConversationResponseObject",
    "ConversationResponseTypedDict",
    "ConversationRestartRequest",
    "ConversationRestartRequestHandoffExecution",
    "ConversationRestartRequestTypedDict",
    "ConversationRestartStreamRequest",
    "ConversationRestartStreamRequestHandoffExecution",
    "ConversationRestartStreamRequestTypedDict",
    "ConversationStreamRequest",
    "ConversationStreamRequestHandoffExecution",
    "ConversationStreamRequestTools",
    "ConversationStreamRequestToolsTypedDict",
    "ConversationStreamRequestTypedDict",
    "ConversationUsageInfo",
    "ConversationUsageInfoTypedDict",
    "Data",
    "DataTypedDict",
    "DeleteFileOut",
    "DeleteFileOutTypedDict",
    "DeleteModelOut",
    "DeleteModelOutTypedDict",
    "DeleteModelV1ModelsModelIDDeleteRequest",
    "DeleteModelV1ModelsModelIDDeleteRequestTypedDict",
    "DeltaMessage",
    "DeltaMessageTypedDict",
    "Document",
    "DocumentLibraryTool",
    "DocumentLibraryToolType",
    "DocumentLibraryToolTypedDict",
    "DocumentOut",
    "DocumentOutTypedDict",
    "DocumentTextContent",
    "DocumentTextContentTypedDict",
    "DocumentTypedDict",
    "DocumentURLChunk",
    "DocumentURLChunkType",
    "DocumentURLChunkTypedDict",
    "DocumentUpdateIn",
    "DocumentUpdateInTypedDict",
    "EmbeddingDtype",
    "EmbeddingRequest",
    "EmbeddingRequestInputs",
    "EmbeddingRequestInputsTypedDict",
    "EmbeddingRequestTypedDict",
    "EmbeddingResponse",
    "EmbeddingResponseData",
    "EmbeddingResponseDataTypedDict",
    "EmbeddingResponseTypedDict",
    "EntityType",
    "Entries",
    "EntriesTypedDict",
    "EventOut",
    "EventOutTypedDict",
    "FIMCompletionRequest",
    "FIMCompletionRequestStop",
    "FIMCompletionRequestStopTypedDict",
    "FIMCompletionRequestTypedDict",
    "FIMCompletionResponse",
    "FIMCompletionResponseTypedDict",
    "FIMCompletionStreamRequest",
    "FIMCompletionStreamRequestStop",
    "FIMCompletionStreamRequestStopTypedDict",
    "FIMCompletionStreamRequestTypedDict",
    "FTClassifierLossFunction",
    "FTModelCapabilitiesOut",
    "FTModelCapabilitiesOutTypedDict",
    "FTModelCard",
    "FTModelCardType",
    "FTModelCardTypedDict",
    "File",
    "FileChunk",
    "FileChunkTypedDict",
    "FilePurpose",
    "FileSchema",
    "FileSchemaTypedDict",
    "FileSignedURL",
    "FileSignedURLTypedDict",
    "FileTypedDict",
    "FilesAPIRoutesDeleteFileRequest",
    "FilesAPIRoutesDeleteFileRequestTypedDict",
    "FilesAPIRoutesDownloadFileRequest",
    "FilesAPIRoutesDownloadFileRequestTypedDict",
    "FilesAPIRoutesGetSignedURLRequest",
    "FilesAPIRoutesGetSignedURLRequestTypedDict",
    "FilesAPIRoutesListFilesRequest",
    "FilesAPIRoutesListFilesRequestTypedDict",
    "FilesAPIRoutesRetrieveFileRequest",
    "FilesAPIRoutesRetrieveFileRequestTypedDict",
    "FilesAPIRoutesUploadFileMultiPartBodyParams",
    "FilesAPIRoutesUploadFileMultiPartBodyParamsTypedDict",
    "FineTuneableModelType",
    "FinishReason",
    "Function",
    "FunctionCall",
    "FunctionCallEntry",
    "FunctionCallEntryArguments",
    "FunctionCallEntryArgumentsTypedDict",
    "FunctionCallEntryObject",
    "FunctionCallEntryType",
    "FunctionCallEntryTypedDict",
    "FunctionCallEvent",
    "FunctionCallEventType",
    "FunctionCallEventTypedDict",
    "FunctionCallTypedDict",
    "FunctionName",
    "FunctionNameTypedDict",
    "FunctionResultEntry",
    "FunctionResultEntryObject",
    "FunctionResultEntryType",
    "FunctionResultEntryTypedDict",
    "FunctionTool",
    "FunctionToolType",
    "FunctionToolTypedDict",
    "FunctionTypedDict",
    "GithubRepositoryIn",
    "GithubRepositoryInType",
    "GithubRepositoryInTypedDict",
    "GithubRepositoryOut",
    "GithubRepositoryOutType",
    "GithubRepositoryOutTypedDict",
    "HTTPValidationError",
    "HTTPValidationErrorData",
    "HandoffExecution",
    "Hyperparameters",
    "HyperparametersTypedDict",
    "ImageGenerationTool",
    "ImageGenerationToolType",
    "ImageGenerationToolTypedDict",
    "ImageURL",
    "ImageURLChunk",
    "ImageURLChunkImageURL",
    "ImageURLChunkImageURLTypedDict",
    "ImageURLChunkType",
    "ImageURLChunkTypedDict",
    "ImageURLTypedDict",
    "InputEntries",
    "InputEntriesTypedDict",
    "Inputs",
    "InputsTypedDict",
    "InstructRequest",
    "InstructRequestInputs",
    "InstructRequestInputsMessages",
    "InstructRequestInputsMessagesTypedDict",
    "InstructRequestInputsTypedDict",
    "InstructRequestMessages",
    "InstructRequestMessagesTypedDict",
    "InstructRequestTypedDict",
    "Integrations",
    "IntegrationsTypedDict",
    "JSONSchema",
    "JSONSchemaTypedDict",
    "JobIn",
    "JobInIntegrations",
    "JobInIntegrationsTypedDict",
    "JobInRepositories",
    "JobInRepositoriesTypedDict",
    "JobInTypedDict",
    "JobMetadataOut",
    "JobMetadataOutTypedDict",
    "JobType",
    "JobsAPIRoutesBatchCancelBatchJobRequest",
    "JobsAPIRoutesBatchCancelBatchJobRequestTypedDict",
    "JobsAPIRoutesBatchGetBatchJobRequest",
    "JobsAPIRoutesBatchGetBatchJobRequestTypedDict",
    "JobsAPIRoutesBatchGetBatchJobsRequest",
    "JobsAPIRoutesBatchGetBatchJobsRequestTypedDict",
    "JobsAPIRoutesFineTuningArchiveFineTunedModelRequest",
    "JobsAPIRoutesFineTuningArchiveFineTunedModelRequestTypedDict",
    "JobsAPIRoutesFineTuningCancelFineTuningJobRequest",
    "JobsAPIRoutesFineTuningCancelFineTuningJobRequestTypedDict",
    "JobsAPIRoutesFineTuningCancelFineTuningJobResponse",
    "JobsAPIRoutesFineTuningCancelFineTuningJobResponseTypedDict",
    "JobsAPIRoutesFineTuningCreateFineTuningJobResponse",
    "JobsAPIRoutesFineTuningCreateFineTuningJobResponseTypedDict",
    "JobsAPIRoutesFineTuningGetFineTuningJobRequest",
    "JobsAPIRoutesFineTuningGetFineTuningJobRequestTypedDict",
    "JobsAPIRoutesFineTuningGetFineTuningJobResponse",
    "JobsAPIRoutesFineTuningGetFineTuningJobResponseTypedDict",
    "JobsAPIRoutesFineTuningGetFineTuningJobsRequest",
    "JobsAPIRoutesFineTuningGetFineTuningJobsRequestTypedDict",
    "JobsAPIRoutesFineTuningStartFineTuningJobRequest",
    "JobsAPIRoutesFineTuningStartFineTuningJobRequestTypedDict",
    "JobsAPIRoutesFineTuningStartFineTuningJobResponse",
    "JobsAPIRoutesFineTuningStartFineTuningJobResponseTypedDict",
    "JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequest",
    "JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequestTypedDict",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelRequest",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelRequestTypedDict",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelResponse",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelResponseTypedDict",
    "JobsOut",
    "JobsOutData",
    "JobsOutDataTypedDict",
    "JobsOutObject",
    "JobsOutTypedDict",
    "LegacyJobMetadataOut",
    "LegacyJobMetadataOutObject",
    "LegacyJobMetadataOutTypedDict",
    "LibrariesDeleteV1Request",
    "LibrariesDeleteV1RequestTypedDict",
    "LibrariesDocumentsDeleteV1Request",
    "LibrariesDocumentsDeleteV1RequestTypedDict",
    "LibrariesDocumentsGetExtractedTextSignedURLV1Request",
    "LibrariesDocumentsGetExtractedTextSignedURLV1RequestTypedDict",
    "LibrariesDocumentsGetSignedURLV1Request",
    "LibrariesDocumentsGetSignedURLV1RequestTypedDict",
    "LibrariesDocumentsGetStatusV1Request",
    "LibrariesDocumentsGetStatusV1RequestTypedDict",
    "LibrariesDocumentsGetTextContentV1Request",
    "LibrariesDocumentsGetTextContentV1RequestTypedDict",
    "LibrariesDocumentsGetV1Request",
    "LibrariesDocumentsGetV1RequestTypedDict",
    "LibrariesDocumentsListV1Request",
    "LibrariesDocumentsListV1RequestTypedDict",
    "LibrariesDocumentsReprocessV1Request",
    "LibrariesDocumentsReprocessV1RequestTypedDict",
    "LibrariesDocumentsUpdateV1Request",
    "LibrariesDocumentsUpdateV1RequestTypedDict",
    "LibrariesDocumentsUploadV1DocumentUpload",
    "LibrariesDocumentsUploadV1DocumentUploadTypedDict",
    "LibrariesDocumentsUploadV1Request",
    "LibrariesDocumentsUploadV1RequestTypedDict",
    "LibrariesGetV1Request",
    "LibrariesGetV1RequestTypedDict",
    "LibrariesShareCreateV1Request",
    "LibrariesShareCreateV1RequestTypedDict",
    "LibrariesShareDeleteV1Request",
    "LibrariesShareDeleteV1RequestTypedDict",
    "LibrariesShareListV1Request",
    "LibrariesShareListV1RequestTypedDict",
    "LibrariesUpdateV1Request",
    "LibrariesUpdateV1RequestTypedDict",
    "LibraryIn",
    "LibraryInTypedDict",
    "LibraryInUpdate",
    "LibraryInUpdateTypedDict",
    "LibraryOut",
    "LibraryOutTypedDict",
    "ListDocumentOut",
    "ListDocumentOutTypedDict",
    "ListFilesOut",
    "ListFilesOutTypedDict",
    "ListLibraryOut",
    "ListLibraryOutTypedDict",
    "ListSharingOut",
    "ListSharingOutTypedDict",
    "Loc",
    "LocTypedDict",
    "MessageEntries",
    "MessageEntriesTypedDict",
    "MessageInputContentChunks",
    "MessageInputContentChunksTypedDict",
    "MessageInputEntry",
    "MessageInputEntryContent",
    "MessageInputEntryContentTypedDict",
    "MessageInputEntryRole",
    "MessageInputEntryType",
    "MessageInputEntryTypedDict",
    "MessageOutputContentChunks",
    "MessageOutputContentChunksTypedDict",
    "MessageOutputEntry",
    "MessageOutputEntryContent",
    "MessageOutputEntryContentTypedDict",
    "MessageOutputEntryObject",
    "MessageOutputEntryRole",
    "MessageOutputEntryType",
    "MessageOutputEntryTypedDict",
    "MessageOutputEvent",
    "MessageOutputEventContent",
    "MessageOutputEventContentTypedDict",
    "MessageOutputEventRole",
    "MessageOutputEventType",
    "MessageOutputEventTypedDict",
    "Messages",
    "MessagesTypedDict",
    "Metadata",
    "MetadataTypedDict",
    "MetricOut",
    "MetricOutTypedDict",
    "MistralPromptMode",
    "ModelCapabilities",
    "ModelCapabilitiesTypedDict",
    "ModelConversation",
    "ModelConversationObject",
    "ModelConversationTools",
    "ModelConversationToolsTypedDict",
    "ModelConversationTypedDict",
    "ModelList",
    "ModelListTypedDict",
    "ModelType",
    "ModerationObject",
    "ModerationObjectTypedDict",
    "ModerationResponse",
    "ModerationResponseTypedDict",
    "OCRImageObject",
    "OCRImageObjectTypedDict",
    "OCRPageDimensions",
    "OCRPageDimensionsTypedDict",
    "OCRPageObject",
    "OCRPageObjectTypedDict",
    "OCRRequest",
    "OCRRequestTypedDict",
    "OCRResponse",
    "OCRResponseTypedDict",
    "OCRUsageInfo",
    "OCRUsageInfoTypedDict",
    "Object",
    "One",
    "OneTypedDict",
    "OutputContentChunks",
    "OutputContentChunksTypedDict",
    "Outputs",
    "OutputsTypedDict",
    "PaginationInfo",
    "PaginationInfoTypedDict",
    "Prediction",
    "PredictionTypedDict",
    "ProcessingStatusOut",
    "ProcessingStatusOutTypedDict",
    "QueryParamStatus",
    "ReferenceChunk",
    "ReferenceChunkType",
    "ReferenceChunkTypedDict",
    "Repositories",
    "RepositoriesTypedDict",
    "Response1",
    "Response1TypedDict",
    "ResponseBody",
    "ResponseBodyTypedDict",
    "ResponseDoneEvent",
    "ResponseDoneEventType",
    "ResponseDoneEventTypedDict",
    "ResponseErrorEvent",
    "ResponseErrorEventType",
    "ResponseErrorEventTypedDict",
    "ResponseFormat",
    "ResponseFormatTypedDict",
    "ResponseFormats",
    "ResponseStartedEvent",
    "ResponseStartedEventType",
    "ResponseStartedEventTypedDict",
    "RetrieveFileOut",
    "RetrieveFileOutTypedDict",
    "RetrieveModelV1ModelsModelIDGetRequest",
    "RetrieveModelV1ModelsModelIDGetRequestTypedDict",
    "RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGet",
    "RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGetTypedDict",
    "Role",
    "SDKError",
    "SSETypes",
    "SampleType",
    "Security",
    "SecurityTypedDict",
    "ShareEnum",
    "SharingDelete",
    "SharingDeleteTypedDict",
    "SharingIn",
    "SharingInTypedDict",
    "SharingOut",
    "SharingOutTypedDict",
    "Source",
    "Status",
    "Stop",
    "StopTypedDict",
    "SystemMessage",
    "SystemMessageContent",
    "SystemMessageContentTypedDict",
    "SystemMessageTypedDict",
    "TextChunk",
    "TextChunkType",
    "TextChunkTypedDict",
    "ThinkChunk",
    "ThinkChunkType",
    "ThinkChunkTypedDict",
    "Thinking",
    "ThinkingTypedDict",
    "TimestampGranularity",
    "Tool",
    "ToolCall",
    "ToolCallTypedDict",
    "ToolChoice",
    "ToolChoiceEnum",
    "ToolChoiceTypedDict",
    "ToolExecutionDeltaEvent",
    "ToolExecutionDeltaEventType",
    "ToolExecutionDeltaEventTypedDict",
    "ToolExecutionDoneEvent",
    "ToolExecutionDoneEventType",
    "ToolExecutionDoneEventTypedDict",
    "ToolExecutionEntry",
    "ToolExecutionEntryObject",
    "ToolExecutionEntryType",
    "ToolExecutionEntryTypedDict",
    "ToolExecutionStartedEvent",
    "ToolExecutionStartedEventType",
    "ToolExecutionStartedEventTypedDict",
    "ToolFileChunk",
    "ToolFileChunkType",
    "ToolFileChunkTypedDict",
    "ToolMessage",
    "ToolMessageContent",
    "ToolMessageContentTypedDict",
    "ToolMessageRole",
    "ToolMessageTypedDict",
    "ToolReferenceChunk",
    "ToolReferenceChunkType",
    "ToolReferenceChunkTypedDict",
    "ToolTypedDict",
    "ToolTypes",
    "Tools",
    "ToolsTypedDict",
    "TrainingFile",
    "TrainingFileTypedDict",
    "TranscriptionResponse",
    "TranscriptionResponseTypedDict",
    "TranscriptionSegmentChunk",
    "TranscriptionSegmentChunkTypedDict",
    "TranscriptionStreamDone",
    "TranscriptionStreamDoneType",
    "TranscriptionStreamDoneTypedDict",
    "TranscriptionStreamEventTypes",
    "TranscriptionStreamEvents",
    "TranscriptionStreamEventsData",
    "TranscriptionStreamEventsDataTypedDict",
    "TranscriptionStreamEventsTypedDict",
    "TranscriptionStreamLanguage",
    "TranscriptionStreamLanguageType",
    "TranscriptionStreamLanguageTypedDict",
    "TranscriptionStreamSegmentDelta",
    "TranscriptionStreamSegmentDeltaType",
    "TranscriptionStreamSegmentDeltaTypedDict",
    "TranscriptionStreamTextDelta",
    "TranscriptionStreamTextDeltaType",
    "TranscriptionStreamTextDeltaTypedDict",
    "Two",
    "TwoTypedDict",
    "Type",
    "UnarchiveFTModelOut",
    "UnarchiveFTModelOutObject",
    "UnarchiveFTModelOutTypedDict",
    "UpdateFTModelIn",
    "UpdateFTModelInTypedDict",
    "UploadFileOut",
    "UploadFileOutTypedDict",
    "UsageInfo",
    "UsageInfoTypedDict",
    "UserMessage",
    "UserMessageContent",
    "UserMessageContentTypedDict",
    "UserMessageRole",
    "UserMessageTypedDict",
    "ValidationError",
    "ValidationErrorTypedDict",
    "WandbIntegration",
    "WandbIntegrationOut",
    "WandbIntegrationOutType",
    "WandbIntegrationOutTypedDict",
    "WandbIntegrationType",
    "WandbIntegrationTypedDict",
    "WebSearchPremiumTool",
    "WebSearchPremiumToolType",
    "WebSearchPremiumToolTypedDict",
    "WebSearchTool",
    "WebSearchToolType",
    "WebSearchToolTypedDict",
]

_dynamic_imports: dict[str, str] = {
    "Agent": ".agent",
    "AgentObject": ".agent",
    "AgentTools": ".agent",
    "AgentToolsTypedDict": ".agent",
    "AgentTypedDict": ".agent",
    "AgentConversation": ".agentconversation",
    "AgentConversationObject": ".agentconversation",
    "AgentConversationTypedDict": ".agentconversation",
    "AgentCreationRequest": ".agentcreationrequest",
    "AgentCreationRequestTools": ".agentcreationrequest",
    "AgentCreationRequestToolsTypedDict": ".agentcreationrequest",
    "AgentCreationRequestTypedDict": ".agentcreationrequest",
    "AgentHandoffDoneEvent": ".agenthandoffdoneevent",
    "AgentHandoffDoneEventType": ".agenthandoffdoneevent",
    "AgentHandoffDoneEventTypedDict": ".agenthandoffdoneevent",
    "AgentHandoffEntry": ".agenthandoffentry",
    "AgentHandoffEntryObject": ".agenthandoffentry",
    "AgentHandoffEntryType": ".agenthandoffentry",
    "AgentHandoffEntryTypedDict": ".agenthandoffentry",
    "AgentHandoffStartedEvent": ".agenthandoffstartedevent",
    "AgentHandoffStartedEventType": ".agenthandoffstartedevent",
    "AgentHandoffStartedEventTypedDict": ".agenthandoffstartedevent",
    "AgentsAPIV1AgentsGetRequest": ".agents_api_v1_agents_getop",
    "AgentsAPIV1AgentsGetRequestTypedDict": ".agents_api_v1_agents_getop",
    "AgentsAPIV1AgentsListRequest": ".agents_api_v1_agents_listop",
    "AgentsAPIV1AgentsListRequestTypedDict": ".agents_api_v1_agents_listop",
    "AgentsAPIV1AgentsUpdateVersionRequest": ".agents_api_v1_agents_update_versionop",
    "AgentsAPIV1AgentsUpdateVersionRequestTypedDict": ".agents_api_v1_agents_update_versionop",
    "AgentsAPIV1AgentsUpdateRequest": ".agents_api_v1_agents_updateop",
    "AgentsAPIV1AgentsUpdateRequestTypedDict": ".agents_api_v1_agents_updateop",
    "AgentsAPIV1ConversationsAppendStreamRequest": ".agents_api_v1_conversations_append_streamop",
    "AgentsAPIV1ConversationsAppendStreamRequestTypedDict": ".agents_api_v1_conversations_append_streamop",
    "AgentsAPIV1ConversationsAppendRequest": ".agents_api_v1_conversations_appendop",
    "AgentsAPIV1ConversationsAppendRequestTypedDict": ".agents_api_v1_conversations_appendop",
    "AgentsAPIV1ConversationsGetRequest": ".agents_api_v1_conversations_getop",
    "AgentsAPIV1ConversationsGetRequestTypedDict": ".agents_api_v1_conversations_getop",
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGet": ".agents_api_v1_conversations_getop",
    "AgentsAPIV1ConversationsGetResponseV1ConversationsGetTypedDict": ".agents_api_v1_conversations_getop",
    "AgentsAPIV1ConversationsHistoryRequest": ".agents_api_v1_conversations_historyop",
    "AgentsAPIV1ConversationsHistoryRequestTypedDict": ".agents_api_v1_conversations_historyop",
    "AgentsAPIV1ConversationsListRequest": ".agents_api_v1_conversations_listop",
    "AgentsAPIV1ConversationsListRequestTypedDict": ".agents_api_v1_conversations_listop",
    "ResponseBody": ".agents_api_v1_conversations_listop",
    "ResponseBodyTypedDict": ".agents_api_v1_conversations_listop",
    "AgentsAPIV1ConversationsMessagesRequest": ".agents_api_v1_conversations_messagesop",
    "AgentsAPIV1ConversationsMessagesRequestTypedDict": ".agents_api_v1_conversations_messagesop",
    "AgentsAPIV1ConversationsRestartStreamRequest": ".agents_api_v1_conversations_restart_streamop",
    "AgentsAPIV1ConversationsRestartStreamRequestTypedDict": ".agents_api_v1_conversations_restart_streamop",
    "AgentsAPIV1ConversationsRestartRequest": ".agents_api_v1_conversations_restartop",
    "AgentsAPIV1ConversationsRestartRequestTypedDict": ".agents_api_v1_conversations_restartop",
    "AgentsCompletionRequest": ".agentscompletionrequest",
    "AgentsCompletionRequestMessages": ".agentscompletionrequest",
    "AgentsCompletionRequestMessagesTypedDict": ".agentscompletionrequest",
    "AgentsCompletionRequestStop": ".agentscompletionrequest",
    "AgentsCompletionRequestStopTypedDict": ".agentscompletionrequest",
    "AgentsCompletionRequestToolChoice": ".agentscompletionrequest",
    "AgentsCompletionRequestToolChoiceTypedDict": ".agentscompletionrequest",
    "AgentsCompletionRequestTypedDict": ".agentscompletionrequest",
    "AgentsCompletionStreamRequest": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestMessages": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestMessagesTypedDict": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestStop": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestStopTypedDict": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestToolChoice": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestToolChoiceTypedDict": ".agentscompletionstreamrequest",
    "AgentsCompletionStreamRequestTypedDict": ".agentscompletionstreamrequest",
    "AgentUpdateRequest": ".agentupdaterequest",
    "AgentUpdateRequestTools": ".agentupdaterequest",
    "AgentUpdateRequestToolsTypedDict": ".agentupdaterequest",
    "AgentUpdateRequestTypedDict": ".agentupdaterequest",
    "APIEndpoint": ".apiendpoint",
    "ArchiveFTModelOut": ".archiveftmodelout",
    "ArchiveFTModelOutObject": ".archiveftmodelout",
    "ArchiveFTModelOutTypedDict": ".archiveftmodelout",
    "AssistantMessage": ".assistantmessage",
    "AssistantMessageContent": ".assistantmessage",
    "AssistantMessageContentTypedDict": ".assistantmessage",
    "AssistantMessageRole": ".assistantmessage",
    "AssistantMessageTypedDict": ".assistantmessage",
    "AudioChunk": ".audiochunk",
    "AudioChunkType": ".audiochunk",
    "AudioChunkTypedDict": ".audiochunk",
    "AudioTranscriptionRequest": ".audiotranscriptionrequest",
    "AudioTranscriptionRequestTypedDict": ".audiotranscriptionrequest",
    "AudioTranscriptionRequestStream": ".audiotranscriptionrequeststream",
    "AudioTranscriptionRequestStreamTypedDict": ".audiotranscriptionrequeststream",
    "BaseModelCard": ".basemodelcard",
    "BaseModelCardType": ".basemodelcard",
    "BaseModelCardTypedDict": ".basemodelcard",
    "BatchError": ".batcherror",
    "BatchErrorTypedDict": ".batcherror",
    "BatchJobIn": ".batchjobin",
    "BatchJobInTypedDict": ".batchjobin",
    "BatchJobOut": ".batchjobout",
    "BatchJobOutObject": ".batchjobout",
    "BatchJobOutTypedDict": ".batchjobout",
    "BatchJobsOut": ".batchjobsout",
    "BatchJobsOutObject": ".batchjobsout",
    "BatchJobsOutTypedDict": ".batchjobsout",
    "BatchJobStatus": ".batchjobstatus",
    "BuiltInConnectors": ".builtinconnectors",
    "ChatClassificationRequest": ".chatclassificationrequest",
    "ChatClassificationRequestTypedDict": ".chatclassificationrequest",
    "ChatCompletionChoice": ".chatcompletionchoice",
    "ChatCompletionChoiceTypedDict": ".chatcompletionchoice",
    "FinishReason": ".chatcompletionchoice",
    "ChatCompletionRequest": ".chatcompletionrequest",
    "ChatCompletionRequestToolChoice": ".chatcompletionrequest",
    "ChatCompletionRequestToolChoiceTypedDict": ".chatcompletionrequest",
    "ChatCompletionRequestTypedDict": ".chatcompletionrequest",
    "Messages": ".chatcompletionrequest",
    "MessagesTypedDict": ".chatcompletionrequest",
    "Stop": ".chatcompletionrequest",
    "StopTypedDict": ".chatcompletionrequest",
    "ChatCompletionResponse": ".chatcompletionresponse",
    "ChatCompletionResponseTypedDict": ".chatcompletionresponse",
    "ChatCompletionStreamRequest": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestMessages": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestMessagesTypedDict": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestStop": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestStopTypedDict": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestToolChoice": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestToolChoiceTypedDict": ".chatcompletionstreamrequest",
    "ChatCompletionStreamRequestTypedDict": ".chatcompletionstreamrequest",
    "ChatModerationRequest": ".chatmoderationrequest",
    "ChatModerationRequestInputs": ".chatmoderationrequest",
    "ChatModerationRequestInputsTypedDict": ".chatmoderationrequest",
    "ChatModerationRequestTypedDict": ".chatmoderationrequest",
    "One": ".chatmoderationrequest",
    "OneTypedDict": ".chatmoderationrequest",
    "Two": ".chatmoderationrequest",
    "TwoTypedDict": ".chatmoderationrequest",
    "CheckpointOut": ".checkpointout",
    "CheckpointOutTypedDict": ".checkpointout",
    "ClassificationRequest": ".classificationrequest",
    "ClassificationRequestInputs": ".classificationrequest",
    "ClassificationRequestInputsTypedDict": ".classificationrequest",
    "ClassificationRequestTypedDict": ".classificationrequest",
    "ClassificationResponse": ".classificationresponse",
    "ClassificationResponseTypedDict": ".classificationresponse",
    "ClassificationTargetResult": ".classificationtargetresult",
    "ClassificationTargetResultTypedDict": ".classificationtargetresult",
    "ClassifierDetailedJobOut": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutIntegrations": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutIntegrationsTypedDict": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutJobType": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutObject": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutStatus": ".classifierdetailedjobout",
    "ClassifierDetailedJobOutTypedDict": ".classifierdetailedjobout",
    "ClassifierFTModelOut": ".classifierftmodelout",
    "ClassifierFTModelOutModelType": ".classifierftmodelout",
    "ClassifierFTModelOutObject": ".classifierftmodelout",
    "ClassifierFTModelOutTypedDict": ".classifierftmodelout",
    "ClassifierJobOut": ".classifierjobout",
    "ClassifierJobOutIntegrations": ".classifierjobout",
    "ClassifierJobOutIntegrationsTypedDict": ".classifierjobout",
    "ClassifierJobOutJobType": ".classifierjobout",
    "ClassifierJobOutObject": ".classifierjobout",
    "ClassifierJobOutStatus": ".classifierjobout",
    "ClassifierJobOutTypedDict": ".classifierjobout",
    "ClassifierTargetIn": ".classifiertargetin",
    "ClassifierTargetInTypedDict": ".classifiertargetin",
    "ClassifierTargetOut": ".classifiertargetout",
    "ClassifierTargetOutTypedDict": ".classifiertargetout",
    "ClassifierTrainingParameters": ".classifiertrainingparameters",
    "ClassifierTrainingParametersTypedDict": ".classifiertrainingparameters",
    "ClassifierTrainingParametersIn": ".classifiertrainingparametersin",
    "ClassifierTrainingParametersInTypedDict": ".classifiertrainingparametersin",
    "CodeInterpreterTool": ".codeinterpretertool",
    "CodeInterpreterToolType": ".codeinterpretertool",
    "CodeInterpreterToolTypedDict": ".codeinterpretertool",
    "CompletionArgs": ".completionargs",
    "CompletionArgsTypedDict": ".completionargs",
    "CompletionArgsStop": ".completionargsstop",
    "CompletionArgsStopTypedDict": ".completionargsstop",
    "CompletionChunk": ".completionchunk",
    "CompletionChunkTypedDict": ".completionchunk",
    "CompletionDetailedJobOut": ".completiondetailedjobout",
    "CompletionDetailedJobOutIntegrations": ".completiondetailedjobout",
    "CompletionDetailedJobOutIntegrationsTypedDict": ".completiondetailedjobout",
    "CompletionDetailedJobOutJobType": ".completiondetailedjobout",
    "CompletionDetailedJobOutObject": ".completiondetailedjobout",
    "CompletionDetailedJobOutRepositories": ".completiondetailedjobout",
    "CompletionDetailedJobOutRepositoriesTypedDict": ".completiondetailedjobout",
    "CompletionDetailedJobOutStatus": ".completiondetailedjobout",
    "CompletionDetailedJobOutTypedDict": ".completiondetailedjobout",
    "CompletionEvent": ".completionevent",
    "CompletionEventTypedDict": ".completionevent",
    "CompletionFTModelOut": ".completionftmodelout",
    "CompletionFTModelOutObject": ".completionftmodelout",
    "CompletionFTModelOutTypedDict": ".completionftmodelout",
    "ModelType": ".completionftmodelout",
    "CompletionJobOut": ".completionjobout",
    "CompletionJobOutObject": ".completionjobout",
    "CompletionJobOutTypedDict": ".completionjobout",
    "Integrations": ".completionjobout",
    "IntegrationsTypedDict": ".completionjobout",
    "JobType": ".completionjobout",
    "Repositories": ".completionjobout",
    "RepositoriesTypedDict": ".completionjobout",
    "Status": ".completionjobout",
    "CompletionResponseStreamChoice": ".completionresponsestreamchoice",
    "CompletionResponseStreamChoiceFinishReason": ".completionresponsestreamchoice",
    "CompletionResponseStreamChoiceTypedDict": ".completionresponsestreamchoice",
    "CompletionTrainingParameters": ".completiontrainingparameters",
    "CompletionTrainingParametersTypedDict": ".completiontrainingparameters",
    "CompletionTrainingParametersIn": ".completiontrainingparametersin",
    "CompletionTrainingParametersInTypedDict": ".completiontrainingparametersin",
    "ContentChunk": ".contentchunk",
    "ContentChunkTypedDict": ".contentchunk",
    "ConversationAppendRequest": ".conversationappendrequest",
    "ConversationAppendRequestHandoffExecution": ".conversationappendrequest",
    "ConversationAppendRequestTypedDict": ".conversationappendrequest",
    "ConversationAppendStreamRequest": ".conversationappendstreamrequest",
    "ConversationAppendStreamRequestHandoffExecution": ".conversationappendstreamrequest",
    "ConversationAppendStreamRequestTypedDict": ".conversationappendstreamrequest",
    "ConversationEvents": ".conversationevents",
    "ConversationEventsData": ".conversationevents",
    "ConversationEventsDataTypedDict": ".conversationevents",
    "ConversationEventsTypedDict": ".conversationevents",
    "ConversationHistory": ".conversationhistory",
    "ConversationHistoryObject": ".conversationhistory",
    "ConversationHistoryTypedDict": ".conversationhistory",
    "Entries": ".conversationhistory",
    "EntriesTypedDict": ".conversationhistory",
    "ConversationInputs": ".conversationinputs",
    "ConversationInputsTypedDict": ".conversationinputs",
    "ConversationMessages": ".conversationmessages",
    "ConversationMessagesObject": ".conversationmessages",
    "ConversationMessagesTypedDict": ".conversationmessages",
    "ConversationRequest": ".conversationrequest",
    "ConversationRequestTypedDict": ".conversationrequest",
    "HandoffExecution": ".conversationrequest",
    "Tools": ".conversationrequest",
    "ToolsTypedDict": ".conversationrequest",
    "ConversationResponse": ".conversationresponse",
    "ConversationResponseObject": ".conversationresponse",
    "ConversationResponseTypedDict": ".conversationresponse",
    "Outputs": ".conversationresponse",
    "OutputsTypedDict": ".conversationresponse",
    "ConversationRestartRequest": ".conversationrestartrequest",
    "ConversationRestartRequestHandoffExecution": ".conversationrestartrequest",
    "ConversationRestartRequestTypedDict": ".conversationrestartrequest",
    "ConversationRestartStreamRequest": ".conversationrestartstreamrequest",
    "ConversationRestartStreamRequestHandoffExecution": ".conversationrestartstreamrequest",
    "ConversationRestartStreamRequestTypedDict": ".conversationrestartstreamrequest",
    "ConversationStreamRequest": ".conversationstreamrequest",
    "ConversationStreamRequestHandoffExecution": ".conversationstreamrequest",
    "ConversationStreamRequestTools": ".conversationstreamrequest",
    "ConversationStreamRequestToolsTypedDict": ".conversationstreamrequest",
    "ConversationStreamRequestTypedDict": ".conversationstreamrequest",
    "ConversationUsageInfo": ".conversationusageinfo",
    "ConversationUsageInfoTypedDict": ".conversationusageinfo",
    "DeleteModelV1ModelsModelIDDeleteRequest": ".delete_model_v1_models_model_id_deleteop",
    "DeleteModelV1ModelsModelIDDeleteRequestTypedDict": ".delete_model_v1_models_model_id_deleteop",
    "DeleteFileOut": ".deletefileout",
    "DeleteFileOutTypedDict": ".deletefileout",
    "DeleteModelOut": ".deletemodelout",
    "DeleteModelOutTypedDict": ".deletemodelout",
    "Content": ".deltamessage",
    "ContentTypedDict": ".deltamessage",
    "DeltaMessage": ".deltamessage",
    "DeltaMessageTypedDict": ".deltamessage",
    "DocumentLibraryTool": ".documentlibrarytool",
    "DocumentLibraryToolType": ".documentlibrarytool",
    "DocumentLibraryToolTypedDict": ".documentlibrarytool",
    "DocumentOut": ".documentout",
    "DocumentOutTypedDict": ".documentout",
    "DocumentTextContent": ".documenttextcontent",
    "DocumentTextContentTypedDict": ".documenttextcontent",
    "DocumentUpdateIn": ".documentupdatein",
    "DocumentUpdateInTypedDict": ".documentupdatein",
    "DocumentURLChunk": ".documenturlchunk",
    "DocumentURLChunkType": ".documenturlchunk",
    "DocumentURLChunkTypedDict": ".documenturlchunk",
    "EmbeddingDtype": ".embeddingdtype",
    "EmbeddingRequest": ".embeddingrequest",
    "EmbeddingRequestInputs": ".embeddingrequest",
    "EmbeddingRequestInputsTypedDict": ".embeddingrequest",
    "EmbeddingRequestTypedDict": ".embeddingrequest",
    "EmbeddingResponse": ".embeddingresponse",
    "EmbeddingResponseTypedDict": ".embeddingresponse",
    "EmbeddingResponseData": ".embeddingresponsedata",
    "EmbeddingResponseDataTypedDict": ".embeddingresponsedata",
    "EntityType": ".entitytype",
    "EventOut": ".eventout",
    "EventOutTypedDict": ".eventout",
    "File": ".file",
    "FileTypedDict": ".file",
    "FileChunk": ".filechunk",
    "FileChunkTypedDict": ".filechunk",
    "FilePurpose": ".filepurpose",
    "FilesAPIRoutesDeleteFileRequest": ".files_api_routes_delete_fileop",
    "FilesAPIRoutesDeleteFileRequestTypedDict": ".files_api_routes_delete_fileop",
    "FilesAPIRoutesDownloadFileRequest": ".files_api_routes_download_fileop",
    "FilesAPIRoutesDownloadFileRequestTypedDict": ".files_api_routes_download_fileop",
    "FilesAPIRoutesGetSignedURLRequest": ".files_api_routes_get_signed_urlop",
    "FilesAPIRoutesGetSignedURLRequestTypedDict": ".files_api_routes_get_signed_urlop",
    "FilesAPIRoutesListFilesRequest": ".files_api_routes_list_filesop",
    "FilesAPIRoutesListFilesRequestTypedDict": ".files_api_routes_list_filesop",
    "FilesAPIRoutesRetrieveFileRequest": ".files_api_routes_retrieve_fileop",
    "FilesAPIRoutesRetrieveFileRequestTypedDict": ".files_api_routes_retrieve_fileop",
    "FilesAPIRoutesUploadFileMultiPartBodyParams": ".files_api_routes_upload_fileop",
    "FilesAPIRoutesUploadFileMultiPartBodyParamsTypedDict": ".files_api_routes_upload_fileop",
    "FileSchema": ".fileschema",
    "FileSchemaTypedDict": ".fileschema",
    "FileSignedURL": ".filesignedurl",
    "FileSignedURLTypedDict": ".filesignedurl",
    "FIMCompletionRequest": ".fimcompletionrequest",
    "FIMCompletionRequestStop": ".fimcompletionrequest",
    "FIMCompletionRequestStopTypedDict": ".fimcompletionrequest",
    "FIMCompletionRequestTypedDict": ".fimcompletionrequest",
    "FIMCompletionResponse": ".fimcompletionresponse",
    "FIMCompletionResponseTypedDict": ".fimcompletionresponse",
    "FIMCompletionStreamRequest": ".fimcompletionstreamrequest",
    "FIMCompletionStreamRequestStop": ".fimcompletionstreamrequest",
    "FIMCompletionStreamRequestStopTypedDict": ".fimcompletionstreamrequest",
    "FIMCompletionStreamRequestTypedDict": ".fimcompletionstreamrequest",
    "FineTuneableModelType": ".finetuneablemodeltype",
    "FTClassifierLossFunction": ".ftclassifierlossfunction",
    "FTModelCapabilitiesOut": ".ftmodelcapabilitiesout",
    "FTModelCapabilitiesOutTypedDict": ".ftmodelcapabilitiesout",
    "FTModelCard": ".ftmodelcard",
    "FTModelCardType": ".ftmodelcard",
    "FTModelCardTypedDict": ".ftmodelcard",
    "Function": ".function",
    "FunctionTypedDict": ".function",
    "Arguments": ".functioncall",
    "ArgumentsTypedDict": ".functioncall",
    "FunctionCall": ".functioncall",
    "FunctionCallTypedDict": ".functioncall",
    "FunctionCallEntry": ".functioncallentry",
    "FunctionCallEntryObject": ".functioncallentry",
    "FunctionCallEntryType": ".functioncallentry",
    "FunctionCallEntryTypedDict": ".functioncallentry",
    "FunctionCallEntryArguments": ".functioncallentryarguments",
    "FunctionCallEntryArgumentsTypedDict": ".functioncallentryarguments",
    "FunctionCallEvent": ".functioncallevent",
    "FunctionCallEventType": ".functioncallevent",
    "FunctionCallEventTypedDict": ".functioncallevent",
    "FunctionName": ".functionname",
    "FunctionNameTypedDict": ".functionname",
    "FunctionResultEntry": ".functionresultentry",
    "FunctionResultEntryObject": ".functionresultentry",
    "FunctionResultEntryType": ".functionresultentry",
    "FunctionResultEntryTypedDict": ".functionresultentry",
    "FunctionTool": ".functiontool",
    "FunctionToolType": ".functiontool",
    "FunctionToolTypedDict": ".functiontool",
    "GithubRepositoryIn": ".githubrepositoryin",
    "GithubRepositoryInType": ".githubrepositoryin",
    "GithubRepositoryInTypedDict": ".githubrepositoryin",
    "GithubRepositoryOut": ".githubrepositoryout",
    "GithubRepositoryOutType": ".githubrepositoryout",
    "GithubRepositoryOutTypedDict": ".githubrepositoryout",
    "HTTPValidationError": ".httpvalidationerror",
    "HTTPValidationErrorData": ".httpvalidationerror",
    "ImageGenerationTool": ".imagegenerationtool",
    "ImageGenerationToolType": ".imagegenerationtool",
    "ImageGenerationToolTypedDict": ".imagegenerationtool",
    "ImageURL": ".imageurl",
    "ImageURLTypedDict": ".imageurl",
    "ImageURLChunk": ".imageurlchunk",
    "ImageURLChunkImageURL": ".imageurlchunk",
    "ImageURLChunkImageURLTypedDict": ".imageurlchunk",
    "ImageURLChunkType": ".imageurlchunk",
    "ImageURLChunkTypedDict": ".imageurlchunk",
    "InputEntries": ".inputentries",
    "InputEntriesTypedDict": ".inputentries",
    "Inputs": ".inputs",
    "InputsTypedDict": ".inputs",
    "InstructRequestInputs": ".inputs",
    "InstructRequestInputsMessages": ".inputs",
    "InstructRequestInputsMessagesTypedDict": ".inputs",
    "InstructRequestInputsTypedDict": ".inputs",
    "InstructRequest": ".instructrequest",
    "InstructRequestMessages": ".instructrequest",
    "InstructRequestMessagesTypedDict": ".instructrequest",
    "InstructRequestTypedDict": ".instructrequest",
    "Hyperparameters": ".jobin",
    "HyperparametersTypedDict": ".jobin",
    "JobIn": ".jobin",
    "JobInIntegrations": ".jobin",
    "JobInIntegrationsTypedDict": ".jobin",
    "JobInRepositories": ".jobin",
    "JobInRepositoriesTypedDict": ".jobin",
    "JobInTypedDict": ".jobin",
    "JobMetadataOut": ".jobmetadataout",
    "JobMetadataOutTypedDict": ".jobmetadataout",
    "JobsAPIRoutesBatchCancelBatchJobRequest": ".jobs_api_routes_batch_cancel_batch_jobop",
    "JobsAPIRoutesBatchCancelBatchJobRequestTypedDict": ".jobs_api_routes_batch_cancel_batch_jobop",
    "JobsAPIRoutesBatchGetBatchJobRequest": ".jobs_api_routes_batch_get_batch_jobop",
    "JobsAPIRoutesBatchGetBatchJobRequestTypedDict": ".jobs_api_routes_batch_get_batch_jobop",
    "JobsAPIRoutesBatchGetBatchJobsRequest": ".jobs_api_routes_batch_get_batch_jobsop",
    "JobsAPIRoutesBatchGetBatchJobsRequestTypedDict": ".jobs_api_routes_batch_get_batch_jobsop",
    "JobsAPIRoutesFineTuningArchiveFineTunedModelRequest": ".jobs_api_routes_fine_tuning_archive_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningArchiveFineTunedModelRequestTypedDict": ".jobs_api_routes_fine_tuning_archive_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningCancelFineTuningJobRequest": ".jobs_api_routes_fine_tuning_cancel_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningCancelFineTuningJobRequestTypedDict": ".jobs_api_routes_fine_tuning_cancel_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningCancelFineTuningJobResponse": ".jobs_api_routes_fine_tuning_cancel_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningCancelFineTuningJobResponseTypedDict": ".jobs_api_routes_fine_tuning_cancel_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningCreateFineTuningJobResponse": ".jobs_api_routes_fine_tuning_create_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningCreateFineTuningJobResponseTypedDict": ".jobs_api_routes_fine_tuning_create_fine_tuning_jobop",
    "Response1": ".jobs_api_routes_fine_tuning_create_fine_tuning_jobop",
    "Response1TypedDict": ".jobs_api_routes_fine_tuning_create_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningGetFineTuningJobRequest": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningGetFineTuningJobRequestTypedDict": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningGetFineTuningJobResponse": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningGetFineTuningJobResponseTypedDict": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningGetFineTuningJobsRequest": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobsop",
    "JobsAPIRoutesFineTuningGetFineTuningJobsRequestTypedDict": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobsop",
    "QueryParamStatus": ".jobs_api_routes_fine_tuning_get_fine_tuning_jobsop",
    "JobsAPIRoutesFineTuningStartFineTuningJobRequest": ".jobs_api_routes_fine_tuning_start_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningStartFineTuningJobRequestTypedDict": ".jobs_api_routes_fine_tuning_start_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningStartFineTuningJobResponse": ".jobs_api_routes_fine_tuning_start_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningStartFineTuningJobResponseTypedDict": ".jobs_api_routes_fine_tuning_start_fine_tuning_jobop",
    "JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequest": ".jobs_api_routes_fine_tuning_unarchive_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningUnarchiveFineTunedModelRequestTypedDict": ".jobs_api_routes_fine_tuning_unarchive_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelRequest": ".jobs_api_routes_fine_tuning_update_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelRequestTypedDict": ".jobs_api_routes_fine_tuning_update_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelResponse": ".jobs_api_routes_fine_tuning_update_fine_tuned_modelop",
    "JobsAPIRoutesFineTuningUpdateFineTunedModelResponseTypedDict": ".jobs_api_routes_fine_tuning_update_fine_tuned_modelop",
    "JobsOut": ".jobsout",
    "JobsOutData": ".jobsout",
    "JobsOutDataTypedDict": ".jobsout",
    "JobsOutObject": ".jobsout",
    "JobsOutTypedDict": ".jobsout",
    "JSONSchema": ".jsonschema",
    "JSONSchemaTypedDict": ".jsonschema",
    "LegacyJobMetadataOut": ".legacyjobmetadataout",
    "LegacyJobMetadataOutObject": ".legacyjobmetadataout",
    "LegacyJobMetadataOutTypedDict": ".legacyjobmetadataout",
    "LibrariesDeleteV1Request": ".libraries_delete_v1op",
    "LibrariesDeleteV1RequestTypedDict": ".libraries_delete_v1op",
    "LibrariesDocumentsDeleteV1Request": ".libraries_documents_delete_v1op",
    "LibrariesDocumentsDeleteV1RequestTypedDict": ".libraries_documents_delete_v1op",
    "LibrariesDocumentsGetExtractedTextSignedURLV1Request": ".libraries_documents_get_extracted_text_signed_url_v1op",
    "LibrariesDocumentsGetExtractedTextSignedURLV1RequestTypedDict": ".libraries_documents_get_extracted_text_signed_url_v1op",
    "LibrariesDocumentsGetSignedURLV1Request": ".libraries_documents_get_signed_url_v1op",
    "LibrariesDocumentsGetSignedURLV1RequestTypedDict": ".libraries_documents_get_signed_url_v1op",
    "LibrariesDocumentsGetStatusV1Request": ".libraries_documents_get_status_v1op",
    "LibrariesDocumentsGetStatusV1RequestTypedDict": ".libraries_documents_get_status_v1op",
    "LibrariesDocumentsGetTextContentV1Request": ".libraries_documents_get_text_content_v1op",
    "LibrariesDocumentsGetTextContentV1RequestTypedDict": ".libraries_documents_get_text_content_v1op",
    "LibrariesDocumentsGetV1Request": ".libraries_documents_get_v1op",
    "LibrariesDocumentsGetV1RequestTypedDict": ".libraries_documents_get_v1op",
    "LibrariesDocumentsListV1Request": ".libraries_documents_list_v1op",
    "LibrariesDocumentsListV1RequestTypedDict": ".libraries_documents_list_v1op",
    "LibrariesDocumentsReprocessV1Request": ".libraries_documents_reprocess_v1op",
    "LibrariesDocumentsReprocessV1RequestTypedDict": ".libraries_documents_reprocess_v1op",
    "LibrariesDocumentsUpdateV1Request": ".libraries_documents_update_v1op",
    "LibrariesDocumentsUpdateV1RequestTypedDict": ".libraries_documents_update_v1op",
    "LibrariesDocumentsUploadV1DocumentUpload": ".libraries_documents_upload_v1op",
    "LibrariesDocumentsUploadV1DocumentUploadTypedDict": ".libraries_documents_upload_v1op",
    "LibrariesDocumentsUploadV1Request": ".libraries_documents_upload_v1op",
    "LibrariesDocumentsUploadV1RequestTypedDict": ".libraries_documents_upload_v1op",
    "LibrariesGetV1Request": ".libraries_get_v1op",
    "LibrariesGetV1RequestTypedDict": ".libraries_get_v1op",
    "LibrariesShareCreateV1Request": ".libraries_share_create_v1op",
    "LibrariesShareCreateV1RequestTypedDict": ".libraries_share_create_v1op",
    "LibrariesShareDeleteV1Request": ".libraries_share_delete_v1op",
    "LibrariesShareDeleteV1RequestTypedDict": ".libraries_share_delete_v1op",
    "LibrariesShareListV1Request": ".libraries_share_list_v1op",
    "LibrariesShareListV1RequestTypedDict": ".libraries_share_list_v1op",
    "LibrariesUpdateV1Request": ".libraries_update_v1op",
    "LibrariesUpdateV1RequestTypedDict": ".libraries_update_v1op",
    "LibraryIn": ".libraryin",
    "LibraryInTypedDict": ".libraryin",
    "LibraryInUpdate": ".libraryinupdate",
    "LibraryInUpdateTypedDict": ".libraryinupdate",
    "LibraryOut": ".libraryout",
    "LibraryOutTypedDict": ".libraryout",
    "ListDocumentOut": ".listdocumentout",
    "ListDocumentOutTypedDict": ".listdocumentout",
    "ListFilesOut": ".listfilesout",
    "ListFilesOutTypedDict": ".listfilesout",
    "ListLibraryOut": ".listlibraryout",
    "ListLibraryOutTypedDict": ".listlibraryout",
    "ListSharingOut": ".listsharingout",
    "ListSharingOutTypedDict": ".listsharingout",
    "MessageEntries": ".messageentries",
    "MessageEntriesTypedDict": ".messageentries",
    "MessageInputContentChunks": ".messageinputcontentchunks",
    "MessageInputContentChunksTypedDict": ".messageinputcontentchunks",
    "MessageInputEntry": ".messageinputentry",
    "MessageInputEntryContent": ".messageinputentry",
    "MessageInputEntryContentTypedDict": ".messageinputentry",
    "MessageInputEntryRole": ".messageinputentry",
    "MessageInputEntryType": ".messageinputentry",
    "MessageInputEntryTypedDict": ".messageinputentry",
    "Object": ".messageinputentry",
    "MessageOutputContentChunks": ".messageoutputcontentchunks",
    "MessageOutputContentChunksTypedDict": ".messageoutputcontentchunks",
    "MessageOutputEntry": ".messageoutputentry",
    "MessageOutputEntryContent": ".messageoutputentry",
    "MessageOutputEntryContentTypedDict": ".messageoutputentry",
    "MessageOutputEntryObject": ".messageoutputentry",
    "MessageOutputEntryRole": ".messageoutputentry",
    "MessageOutputEntryType": ".messageoutputentry",
    "MessageOutputEntryTypedDict": ".messageoutputentry",
    "MessageOutputEvent": ".messageoutputevent",
    "MessageOutputEventContent": ".messageoutputevent",
    "MessageOutputEventContentTypedDict": ".messageoutputevent",
    "MessageOutputEventRole": ".messageoutputevent",
    "MessageOutputEventType": ".messageoutputevent",
    "MessageOutputEventTypedDict": ".messageoutputevent",
    "MetricOut": ".metricout",
    "MetricOutTypedDict": ".metricout",
    "MistralPromptMode": ".mistralpromptmode",
    "ModelCapabilities": ".modelcapabilities",
    "ModelCapabilitiesTypedDict": ".modelcapabilities",
    "ModelConversation": ".modelconversation",
    "ModelConversationObject": ".modelconversation",
    "ModelConversationTools": ".modelconversation",
    "ModelConversationToolsTypedDict": ".modelconversation",
    "ModelConversationTypedDict": ".modelconversation",
    "Data": ".modellist",
    "DataTypedDict": ".modellist",
    "ModelList": ".modellist",
    "ModelListTypedDict": ".modellist",
    "ModerationObject": ".moderationobject",
    "ModerationObjectTypedDict": ".moderationobject",
    "ModerationResponse": ".moderationresponse",
    "ModerationResponseTypedDict": ".moderationresponse",
    "OCRImageObject": ".ocrimageobject",
    "OCRImageObjectTypedDict": ".ocrimageobject",
    "OCRPageDimensions": ".ocrpagedimensions",
    "OCRPageDimensionsTypedDict": ".ocrpagedimensions",
    "OCRPageObject": ".ocrpageobject",
    "OCRPageObjectTypedDict": ".ocrpageobject",
    "Document": ".ocrrequest",
    "DocumentTypedDict": ".ocrrequest",
    "OCRRequest": ".ocrrequest",
    "OCRRequestTypedDict": ".ocrrequest",
    "OCRResponse": ".ocrresponse",
    "OCRResponseTypedDict": ".ocrresponse",
    "OCRUsageInfo": ".ocrusageinfo",
    "OCRUsageInfoTypedDict": ".ocrusageinfo",
    "OutputContentChunks": ".outputcontentchunks",
    "OutputContentChunksTypedDict": ".outputcontentchunks",
    "PaginationInfo": ".paginationinfo",
    "PaginationInfoTypedDict": ".paginationinfo",
    "Prediction": ".prediction",
    "PredictionTypedDict": ".prediction",
    "ProcessingStatusOut": ".processingstatusout",
    "ProcessingStatusOutTypedDict": ".processingstatusout",
    "ReferenceChunk": ".referencechunk",
    "ReferenceChunkType": ".referencechunk",
    "ReferenceChunkTypedDict": ".referencechunk",
    "ResponseDoneEvent": ".responsedoneevent",
    "ResponseDoneEventType": ".responsedoneevent",
    "ResponseDoneEventTypedDict": ".responsedoneevent",
    "ResponseErrorEvent": ".responseerrorevent",
    "ResponseErrorEventType": ".responseerrorevent",
    "ResponseErrorEventTypedDict": ".responseerrorevent",
    "ResponseFormat": ".responseformat",
    "ResponseFormatTypedDict": ".responseformat",
    "ResponseFormats": ".responseformats",
    "ResponseStartedEvent": ".responsestartedevent",
    "ResponseStartedEventType": ".responsestartedevent",
    "ResponseStartedEventTypedDict": ".responsestartedevent",
    "RetrieveModelV1ModelsModelIDGetRequest": ".retrieve_model_v1_models_model_id_getop",
    "RetrieveModelV1ModelsModelIDGetRequestTypedDict": ".retrieve_model_v1_models_model_id_getop",
    "RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGet": ".retrieve_model_v1_models_model_id_getop",
    "RetrieveModelV1ModelsModelIDGetResponseRetrieveModelV1ModelsModelIDGetTypedDict": ".retrieve_model_v1_models_model_id_getop",
    "RetrieveFileOut": ".retrievefileout",
    "RetrieveFileOutTypedDict": ".retrievefileout",
    "SampleType": ".sampletype",
    "SDKError": ".sdkerror",
    "Security": ".security",
    "SecurityTypedDict": ".security",
    "ShareEnum": ".shareenum",
    "SharingDelete": ".sharingdelete",
    "SharingDeleteTypedDict": ".sharingdelete",
    "SharingIn": ".sharingin",
    "SharingInTypedDict": ".sharingin",
    "SharingOut": ".sharingout",
    "SharingOutTypedDict": ".sharingout",
    "Source": ".source",
    "SSETypes": ".ssetypes",
    "Role": ".systemmessage",
    "SystemMessage": ".systemmessage",
    "SystemMessageContent": ".systemmessage",
    "SystemMessageContentTypedDict": ".systemmessage",
    "SystemMessageTypedDict": ".systemmessage",
    "TextChunk": ".textchunk",
    "TextChunkType": ".textchunk",
    "TextChunkTypedDict": ".textchunk",
    "ThinkChunk": ".thinkchunk",
    "ThinkChunkType": ".thinkchunk",
    "ThinkChunkTypedDict": ".thinkchunk",
    "Thinking": ".thinkchunk",
    "ThinkingTypedDict": ".thinkchunk",
    "TimestampGranularity": ".timestampgranularity",
    "Tool": ".tool",
    "ToolTypedDict": ".tool",
    "Metadata": ".toolcall",
    "MetadataTypedDict": ".toolcall",
    "ToolCall": ".toolcall",
    "ToolCallTypedDict": ".toolcall",
    "ToolChoice": ".toolchoice",
    "ToolChoiceTypedDict": ".toolchoice",
    "ToolChoiceEnum": ".toolchoiceenum",
    "ToolExecutionDeltaEvent": ".toolexecutiondeltaevent",
    "ToolExecutionDeltaEventType": ".toolexecutiondeltaevent",
    "ToolExecutionDeltaEventTypedDict": ".toolexecutiondeltaevent",
    "ToolExecutionDoneEvent": ".toolexecutiondoneevent",
    "ToolExecutionDoneEventType": ".toolexecutiondoneevent",
    "ToolExecutionDoneEventTypedDict": ".toolexecutiondoneevent",
    "ToolExecutionEntry": ".toolexecutionentry",
    "ToolExecutionEntryObject": ".toolexecutionentry",
    "ToolExecutionEntryType": ".toolexecutionentry",
    "ToolExecutionEntryTypedDict": ".toolexecutionentry",
    "ToolExecutionStartedEvent": ".toolexecutionstartedevent",
    "ToolExecutionStartedEventType": ".toolexecutionstartedevent",
    "ToolExecutionStartedEventTypedDict": ".toolexecutionstartedevent",
    "ToolFileChunk": ".toolfilechunk",
    "ToolFileChunkType": ".toolfilechunk",
    "ToolFileChunkTypedDict": ".toolfilechunk",
    "ToolMessage": ".toolmessage",
    "ToolMessageContent": ".toolmessage",
    "ToolMessageContentTypedDict": ".toolmessage",
    "ToolMessageRole": ".toolmessage",
    "ToolMessageTypedDict": ".toolmessage",
    "ToolReferenceChunk": ".toolreferencechunk",
    "ToolReferenceChunkType": ".toolreferencechunk",
    "ToolReferenceChunkTypedDict": ".toolreferencechunk",
    "ToolTypes": ".tooltypes",
    "TrainingFile": ".trainingfile",
    "TrainingFileTypedDict": ".trainingfile",
    "TranscriptionResponse": ".transcriptionresponse",
    "TranscriptionResponseTypedDict": ".transcriptionresponse",
    "TranscriptionSegmentChunk": ".transcriptionsegmentchunk",
    "TranscriptionSegmentChunkTypedDict": ".transcriptionsegmentchunk",
    "Type": ".transcriptionsegmentchunk",
    "TranscriptionStreamDone": ".transcriptionstreamdone",
    "TranscriptionStreamDoneType": ".transcriptionstreamdone",
    "TranscriptionStreamDoneTypedDict": ".transcriptionstreamdone",
    "TranscriptionStreamEvents": ".transcriptionstreamevents",
    "TranscriptionStreamEventsData": ".transcriptionstreamevents",
    "TranscriptionStreamEventsDataTypedDict": ".transcriptionstreamevents",
    "TranscriptionStreamEventsTypedDict": ".transcriptionstreamevents",
    "TranscriptionStreamEventTypes": ".transcriptionstreameventtypes",
    "TranscriptionStreamLanguage": ".transcriptionstreamlanguage",
    "TranscriptionStreamLanguageType": ".transcriptionstreamlanguage",
    "TranscriptionStreamLanguageTypedDict": ".transcriptionstreamlanguage",
    "TranscriptionStreamSegmentDelta": ".transcriptionstreamsegmentdelta",
    "TranscriptionStreamSegmentDeltaType": ".transcriptionstreamsegmentdelta",
    "TranscriptionStreamSegmentDeltaTypedDict": ".transcriptionstreamsegmentdelta",
    "TranscriptionStreamTextDelta": ".transcriptionstreamtextdelta",
    "TranscriptionStreamTextDeltaType": ".transcriptionstreamtextdelta",
    "TranscriptionStreamTextDeltaTypedDict": ".transcriptionstreamtextdelta",
    "UnarchiveFTModelOut": ".unarchiveftmodelout",
    "UnarchiveFTModelOutObject": ".unarchiveftmodelout",
    "UnarchiveFTModelOutTypedDict": ".unarchiveftmodelout",
    "UpdateFTModelIn": ".updateftmodelin",
    "UpdateFTModelInTypedDict": ".updateftmodelin",
    "UploadFileOut": ".uploadfileout",
    "UploadFileOutTypedDict": ".uploadfileout",
    "UsageInfo": ".usageinfo",
    "UsageInfoTypedDict": ".usageinfo",
    "UserMessage": ".usermessage",
    "UserMessageContent": ".usermessage",
    "UserMessageContentTypedDict": ".usermessage",
    "UserMessageRole": ".usermessage",
    "UserMessageTypedDict": ".usermessage",
    "Loc": ".validationerror",
    "LocTypedDict": ".validationerror",
    "ValidationError": ".validationerror",
    "ValidationErrorTypedDict": ".validationerror",
    "WandbIntegration": ".wandbintegration",
    "WandbIntegrationType": ".wandbintegration",
    "WandbIntegrationTypedDict": ".wandbintegration",
    "WandbIntegrationOut": ".wandbintegrationout",
    "WandbIntegrationOutType": ".wandbintegrationout",
    "WandbIntegrationOutTypedDict": ".wandbintegrationout",
    "WebSearchPremiumTool": ".websearchpremiumtool",
    "WebSearchPremiumToolType": ".websearchpremiumtool",
    "WebSearchPremiumToolTypedDict": ".websearchpremiumtool",
    "WebSearchTool": ".websearchtool",
    "WebSearchToolType": ".websearchtool",
    "WebSearchToolTypedDict": ".websearchtool",
}


def __getattr__(attr_name: str) -> object:
    module_name = _dynamic_imports.get(attr_name)
    if module_name is None:
        raise AttributeError(
            f"No {attr_name} found in _dynamic_imports for module name -> {__name__} "
        )

    try:
        module = import_module(module_name, __package__)
        result = getattr(module, attr_name)
        return result
    except ImportError as e:
        raise ImportError(
            f"Failed to import {attr_name} from {module_name}: {e}"
        ) from e
    except AttributeError as e:
        raise AttributeError(
            f"Failed to get {attr_name} from {module_name}: {e}"
        ) from e


def __dir__():
    lazy_attrs = list(_dynamic_imports.keys())
    return sorted(lazy_attrs)
