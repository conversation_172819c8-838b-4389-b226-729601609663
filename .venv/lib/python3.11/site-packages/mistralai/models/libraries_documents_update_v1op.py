"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .documentupdatein import DocumentUpdateIn, DocumentUpdateInTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class LibrariesDocumentsUpdateV1RequestTypedDict(TypedDict):
    library_id: str
    document_id: str
    document_update_in: DocumentUpdateInTypedDict


class LibrariesDocumentsUpdateV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    document_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    document_update_in: Annotated[
        DocumentUpdateIn,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
