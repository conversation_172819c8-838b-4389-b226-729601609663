"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .referencechunk import ReferenceChunk, ReferenceChunkTypedDict
from .textchunk import TextChunk, TextChunkTypedDict
from mistralai.types import BaseModel
from typing import List, Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


ThinkingTypedDict = TypeAliasType(
    "ThinkingTypedDict", Union[ReferenceChunkTypedDict, TextChunkTypedDict]
)


Thinking = TypeAliasType("Thinking", Union[ReferenceChunk, TextChunk])


ThinkChunkType = Literal["thinking"]


class ThinkChunkTypedDict(TypedDict):
    thinking: List[ThinkingTypedDict]
    closed: NotRequired[bool]
    r"""Whether the thinking chunk is closed or not. Currently only used for prefixing."""
    type: NotRequired[ThinkChunkType]


class ThinkChunk(BaseModel):
    thinking: List[Thinking]

    closed: Optional[bool] = None
    r"""Whether the thinking chunk is closed or not. Currently only used for prefixing."""

    type: Optional[ThinkChunkType] = "thinking"
