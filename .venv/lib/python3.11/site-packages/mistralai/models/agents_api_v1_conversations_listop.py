"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agentconversation import AgentConversation, AgentConversationTypedDict
from .modelconversation import ModelConversation, ModelConversationTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, QueryParamMetadata
from typing import Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


class AgentsAPIV1ConversationsListRequestTypedDict(TypedDict):
    page: NotRequired[int]
    page_size: NotRequired[int]


class AgentsAPIV1ConversationsListRequest(BaseModel):
    page: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 0

    page_size: Annotated[
        Optional[int],
        FieldMetadata(query=QueryParamMetadata(style="form", explode=True)),
    ] = 100


ResponseBodyTypedDict = TypeAliasType(
    "ResponseBodyTypedDict",
    Union[AgentConversationTypedDict, ModelConversationTypedDict],
)


ResponseBody = TypeAliasType(
    "ResponseBody", Union[AgentConversation, ModelConversation]
)
