"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .transcriptionsegmentchunk import (
    TranscriptionSegmentChunk,
    TranscriptionSegmentChunkTypedDict,
)
from .usageinfo import UsageInfo, UsageInfoTypedDict
from mistralai.types import BaseModel, Nullable, UNSET_SENTINEL
import pydantic
from pydantic import ConfigDict, model_serializer
from typing import Any, Dict, List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


TranscriptionStreamDoneType = Literal["transcription.done"]


class TranscriptionStreamDoneTypedDict(TypedDict):
    model: str
    text: str
    usage: UsageInfoTypedDict
    language: Nullable[str]
    segments: NotRequired[List[TranscriptionSegmentChunkTypedDict]]
    type: NotRequired[TranscriptionStreamDoneType]


class TranscriptionStreamDone(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True, arbitrary_types_allowed=True, extra="allow"
    )
    __pydantic_extra__: Dict[str, Any] = pydantic.Field(init=False)

    model: str

    text: str

    usage: UsageInfo

    language: Nullable[str]

    segments: Optional[List[TranscriptionSegmentChunk]] = None

    type: Optional[TranscriptionStreamDoneType] = "transcription.done"

    @property
    def additional_properties(self):
        return self.__pydantic_extra__

    @additional_properties.setter
    def additional_properties(self, value):
        self.__pydantic_extra__ = value  # pyright: ignore[reportIncompatibleVariableOverride]

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["segments", "type"]
        nullable_fields = ["language"]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in type(self).model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        for k, v in serialized.items():
            m[k] = v

        return m
