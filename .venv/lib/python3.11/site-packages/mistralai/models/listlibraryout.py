"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .libraryout import LibraryOut, LibraryOutTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ListLibraryOutTypedDict(TypedDict):
    data: List[LibraryOutTypedDict]


class ListLibraryOut(BaseModel):
    data: List[LibraryOut]
