"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .documenturlchunk import DocumentUR<PERSON>hunk, DocumentURLChunkTypedDict
from .imageurlchunk import ImageUR<PERSON>hunk, ImageUR<PERSON>hunkTypedDict
from .textchunk import <PERSON><PERSON>hunk, TextChunkTypedDict
from .toolfilechunk import ToolFileChunk, ToolFileChunkTypedDict
from .toolreferencechunk import Tool<PERSON><PERSON>erence<PERSON>hunk, ToolReferenceChunkTypedDict
from typing import Union
from typing_extensions import TypeAliasType


OutputContentChunksTypedDict = TypeAliasType(
    "OutputContentChunksTypedDict",
    Union[
        TextChunkTypedDict,
        ImageURLChunkTypedDict,
        DocumentURLChunkTypedDict,
        ToolFileChunkTypedDict,
        ToolReferenceChunkTypedDict,
    ],
)


OutputContentChunks = TypeAliasType(
    "OutputContentChunks",
    Union[
        TextChunk, ImageURLChunk, <PERSON>ument<PERSON><PERSON>hunk, <PERSON><PERSON><PERSON><PERSON><PERSON>hun<PERSON>, ToolReferenceChunk
    ],
)
