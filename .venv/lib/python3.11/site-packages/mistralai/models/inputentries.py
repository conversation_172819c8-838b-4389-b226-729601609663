"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agenthandoffentry import Agent<PERSON>andoffEntry, AgentHandoffEntryTypedDict
from .functioncallentry import FunctionCallEntry, FunctionCallEntryTypedDict
from .functionresultentry import FunctionR<PERSON>ultEntry, FunctionResultEntryTypedDict
from .messageinputentry import MessageInputEntry, MessageInputEntryTypedDict
from .messageoutputentry import MessageOutputEntry, MessageOutputEntryTypedDict
from .toolexecutionentry import ToolExecutionEntry, ToolExecutionEntryTypedDict
from typing import Union
from typing_extensions import TypeAliasType


InputEntriesTypedDict = TypeAliasType(
    "InputEntriesTypedDict",
    Union[
        FunctionResultEntryTypedDict,
        MessageInputEntryTypedDict,
        FunctionCallEntryTypedDict,
        ToolExecutionEntryTypedDict,
        MessageOutputEntryTypedDict,
        AgentHandoffEntryTypedDict,
    ],
)


InputEntries = TypeAliasType(
    "InputEntries",
    Union[
        FunctionResultEntry,
        MessageInputEntry,
        FunctionCallEntry,
        ToolExecutionEntry,
        MessageOutputEntry,
        AgentHandoffEntry,
    ],
)
