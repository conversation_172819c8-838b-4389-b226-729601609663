"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata
from typing_extensions import Annotated, TypedDict


class LibrariesShareListV1RequestTypedDict(TypedDict):
    library_id: str


class LibrariesShareListV1Request(BaseModel):
    library_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
