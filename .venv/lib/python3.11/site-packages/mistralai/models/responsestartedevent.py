"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ResponseStartedEventType = Literal["conversation.response.started"]


class ResponseStartedEventTypedDict(TypedDict):
    conversation_id: str
    type: NotRequired[ResponseStartedEventType]
    created_at: NotRequired[datetime]


class ResponseStartedEvent(BaseModel):
    conversation_id: str

    type: Optional[ResponseStartedEventType] = "conversation.response.started"

    created_at: Optional[datetime] = None
