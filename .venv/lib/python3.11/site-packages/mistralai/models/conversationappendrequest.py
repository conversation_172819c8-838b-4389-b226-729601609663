"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .completionargs import CompletionArgs, CompletionArgsTypedDict
from .conversationinputs import ConversationInputs, ConversationInputsTypedDict
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ConversationAppendRequestHandoffExecution = Literal["client", "server"]


class ConversationAppendRequestTypedDict(TypedDict):
    inputs: ConversationInputsTypedDict
    stream: NotRequired[bool]
    store: NotRequired[bool]
    r"""Whether to store the results into our servers or not."""
    handoff_execution: NotRequired[ConversationAppendRequestHandoffExecution]
    completion_args: NotRequired[CompletionArgsTypedDict]
    r"""White-listed arguments from the completion API"""


class ConversationAppendRequest(BaseModel):
    inputs: ConversationInputs

    stream: Optional[bool] = False

    store: Optional[bool] = True
    r"""Whether to store the results into our servers or not."""

    handoff_execution: Optional[ConversationAppendRequestHandoffExecution] = "server"

    completion_args: Optional[CompletionArgs] = None
    r"""White-listed arguments from the completion API"""
