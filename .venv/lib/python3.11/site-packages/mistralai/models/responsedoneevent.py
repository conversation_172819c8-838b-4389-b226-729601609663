"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .conversationusageinfo import ConversationUsageInfo, ConversationUsageInfoTypedDict
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


ResponseDoneEventType = Literal["conversation.response.done"]


class ResponseDoneEventTypedDict(TypedDict):
    usage: ConversationUsageInfoTypedDict
    type: NotRequired[ResponseDoneEventType]
    created_at: NotRequired[datetime]


class ResponseDoneEvent(BaseModel):
    usage: ConversationUsageInfo

    type: Optional[ResponseDoneEventType] = "conversation.response.done"

    created_at: Optional[datetime] = None
