"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


WebSearchPremiumToolType = Literal["web_search_premium"]


class WebSearchPremiumToolTypedDict(TypedDict):
    type: NotRequired[WebSearchPremiumToolType]


class WebSearchPremiumTool(BaseModel):
    type: Optional[WebSearchPremiumToolType] = "web_search_premium"
