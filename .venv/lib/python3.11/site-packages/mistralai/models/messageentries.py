"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .messageinputentry import MessageInputEntry, MessageInputEntryTypedDict
from .messageoutputentry import MessageOutputEntry, MessageOutputEntryTypedDict
from typing import Union
from typing_extensions import TypeAliasType


MessageEntriesTypedDict = TypeAliasType(
    "MessageEntriesTypedDict",
    Union[MessageInputEntryTypedDict, MessageOutputEntryTypedDict],
)


MessageEntries = TypeAliasType(
    "MessageEntries", Union[MessageInputEntry, MessageOutputEntry]
)
