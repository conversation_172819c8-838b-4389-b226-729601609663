"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .moderationobject import ModerationObject, ModerationObjectTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ModerationResponseTypedDict(TypedDict):
    id: str
    model: str
    results: List[ModerationObjectTypedDict]


class ModerationResponse(BaseModel):
    id: str

    model: str

    results: List[ModerationObject]
