"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .basemodelcard import BaseModelCard, BaseModelCardTypedDict
from .ftmodelcard import FTModelCard, FTModelCardTypedDict
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import List, Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


DataTypedDict = TypeAliasType(
    "DataTypedDict", Union[BaseModelCardTypedDict, FTModelCardTypedDict]
)


Data = Annotated[
    Union[
        Annotated[BaseModelCard, Tag("base")], Annotated[FTModelCard, Tag("fine-tuned")]
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]


class ModelListTypedDict(TypedDict):
    object: NotRequired[str]
    data: NotRequired[List[DataTypedDict]]


class ModelList(BaseModel):
    object: Optional[str] = "list"

    data: Optional[List[Data]] = None
