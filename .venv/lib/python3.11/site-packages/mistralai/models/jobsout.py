"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .classifierjobout import ClassifierJobOut, ClassifierJobOutTypedDict
from .completionjobout import CompletionJobOut, CompletionJobOutTypedDict
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import List, Literal, Optional, Union
from typing_extensions import Annotated, NotRequired, TypeAliasType, TypedDict


JobsOutDataTypedDict = TypeAliasType(
    "JobsOutDataTypedDict", Union[ClassifierJobOutTypedDict, CompletionJobOutTypedDict]
)


JobsOutData = Annotated[
    Union[
        Annotated[ClassifierJobOut, Tag("classifier")],
        Annotated[CompletionJobOut, Tag("completion")],
    ],
    Discriminator(lambda m: get_discriminator(m, "job_type", "job_type")),
]


JobsOutObject = Literal["list"]


class JobsOutTypedDict(TypedDict):
    total: int
    data: NotRequired[List[JobsOutDataTypedDict]]
    object: NotRequired[JobsOutObject]


class JobsOut(BaseModel):
    total: int

    data: Optional[List[JobsOutData]] = None

    object: Optional[JobsOutObject] = "list"
