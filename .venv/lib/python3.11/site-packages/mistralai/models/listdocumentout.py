"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .documentout import DocumentOut, DocumentOutTypedDict
from .paginationinfo import PaginationInfo, PaginationInfoTypedDict
from mistralai.types import BaseModel
from typing import List
from typing_extensions import TypedDict


class ListDocumentOutTypedDict(TypedDict):
    pagination: PaginationInfoTypedDict
    data: List[DocumentOutTypedDict]


class ListDocumentOut(BaseModel):
    pagination: PaginationInfo

    data: List[DocumentOut]
