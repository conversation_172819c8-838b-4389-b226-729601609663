"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .transcriptionstreamdone import (
    TranscriptionStreamDone,
    TranscriptionStreamDoneTypedDict,
)
from .transcriptionstreameventtypes import TranscriptionStreamEventTypes
from .transcriptionstreamlanguage import (
    TranscriptionStreamLanguage,
    TranscriptionStreamLanguageTypedDict,
)
from .transcriptionstreamsegmentdelta import (
    TranscriptionStreamSegmentDelta,
    TranscriptionStreamSegmentDeltaTypedDict,
)
from .transcriptionstreamtextdelta import (
    TranscriptionStreamTextDelta,
    TranscriptionStreamTextDeltaTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType, TypedDict


TranscriptionStreamEventsDataTypedDict = TypeAliasType(
    "TranscriptionStreamEventsDataTypedDict",
    Union[
        TranscriptionStreamTextDeltaTypedDict,
        TranscriptionStreamLanguageTypedDict,
        TranscriptionStreamSegmentDeltaTypedDict,
        TranscriptionStreamDoneTypedDict,
    ],
)


TranscriptionStreamEventsData = Annotated[
    Union[
        Annotated[TranscriptionStreamDone, Tag("transcription.done")],
        Annotated[TranscriptionStreamLanguage, Tag("transcription.language")],
        Annotated[TranscriptionStreamSegmentDelta, Tag("transcription.segment")],
        Annotated[TranscriptionStreamTextDelta, Tag("transcription.text.delta")],
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]


class TranscriptionStreamEventsTypedDict(TypedDict):
    event: TranscriptionStreamEventTypes
    data: TranscriptionStreamEventsDataTypedDict


class TranscriptionStreamEvents(BaseModel):
    event: TranscriptionStreamEventTypes

    data: TranscriptionStreamEventsData
