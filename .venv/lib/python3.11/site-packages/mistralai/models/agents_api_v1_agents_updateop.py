"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agentupdaterequest import AgentUpdateRe<PERSON>, AgentUpdateRequestTypedDict
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1AgentsUpdateRequestTypedDict(TypedDict):
    agent_id: str
    agent_update_request: AgentUpdateRequestTypedDict


class AgentsAPIV1AgentsUpdateRequest(BaseModel):
    agent_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]

    agent_update_request: Annotated[
        AgentUpdateRequest,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
