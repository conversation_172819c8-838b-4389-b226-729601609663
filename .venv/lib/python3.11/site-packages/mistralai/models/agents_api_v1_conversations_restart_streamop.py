"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .conversationrestartstreamrequest import (
    ConversationRestartStreamRequest,
    ConversationRestartStreamRequestTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1ConversationsRestartStreamRequestTypedDict(TypedDict):
    conversation_id: str
    r"""ID of the original conversation which is being restarted."""
    conversation_restart_stream_request: ConversationRestartStreamRequestTypedDict


class AgentsAPIV1ConversationsRestartStreamRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""ID of the original conversation which is being restarted."""

    conversation_restart_stream_request: Annotated[
        ConversationRestartStreamRequest,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
