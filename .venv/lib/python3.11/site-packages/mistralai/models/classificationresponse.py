"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .classificationtargetresult import (
    ClassificationTargetResult,
    ClassificationTargetResultTypedDict,
)
from mistralai.types import BaseModel
from typing import Dict, List
from typing_extensions import TypedDict


class ClassificationResponseTypedDict(TypedDict):
    id: str
    model: str
    results: List[Dict[str, ClassificationTargetResultTypedDict]]


class ClassificationResponse(BaseModel):
    id: str

    model: str

    results: List[Dict[str, ClassificationTargetResult]]
