"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import List, Optional
from typing_extensions import NotRequired, TypedDict


class EmbeddingResponseDataTypedDict(TypedDict):
    object: NotRequired[str]
    embedding: NotRequired[List[float]]
    index: NotRequired[int]


class EmbeddingResponseData(BaseModel):
    object: Optional[str] = None

    embedding: Optional[List[float]] = None

    index: Optional[int] = None
