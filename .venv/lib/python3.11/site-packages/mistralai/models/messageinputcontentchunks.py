"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .documenturlchunk import DocumentURLChunk, DocumentURLChunkTypedDict
from .imageurlchunk import ImageUR<PERSON>hunk, ImageURLChunkTypedDict
from .textchunk import TextChunk, TextChunkTypedDict
from .toolfilechunk import ToolFileChunk, ToolFileChunkTypedDict
from typing import Union
from typing_extensions import TypeAliasType


MessageInputContentChunksTypedDict = TypeAliasType(
    "MessageInputContentChunksTypedDict",
    Union[
        TextChunkTypedDict,
        ImageURLChunkTypedDict,
        DocumentURLChunkTypedDict,
        ToolFileChunkTypedDict,
    ],
)


MessageInputContentChunks = TypeAliasType(
    "MessageInputContentChunks",
    Union[TextChunk, ImageURLChunk, DocumentURLChunk, ToolFileChunk],
)
