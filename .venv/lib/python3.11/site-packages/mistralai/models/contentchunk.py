"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .audiochunk import AudioChunk, AudioChunkTypedDict
from .documenturlchunk import DocumentURLChunk, DocumentURLChunkTypedDict
from .filechunk import FileChunk, FileChunkTypedDict
from .imageurlchunk import ImageURLChunk, ImageURLChunkTypedDict
from .referencechunk import ReferenceChunk, ReferenceChunkTypedDict
from .textchunk import TextChunk, TextChunkTypedDict
from .thinkchunk import ThinkChunk, ThinkChunkTypedDict
from mistralai.utils import get_discriminator
from pydantic import Discriminator, Tag
from typing import Union
from typing_extensions import Annotated, TypeAliasType


ContentChunkTypedDict = TypeAliasType(
    "ContentChunkTypedDict",
    Union[
        TextChunkTypedDict,
        ImageURLChunkTypedDict,
        ReferenceChunkTypedDict,
        FileChunkTypedDict,
        AudioChunkTypedDict,
        DocumentURLChunkTypedDict,
        ThinkChunkTypedDict,
    ],
)


ContentChunk = Annotated[
    Union[
        Annotated[ImageURLChunk, Tag("image_url")],
        Annotated[DocumentURLChunk, Tag("document_url")],
        Annotated[TextChunk, Tag("text")],
        Annotated[ReferenceChunk, Tag("reference")],
        Annotated[FileChunk, Tag("file")],
        Annotated[ThinkChunk, Tag("thinking")],
        Annotated[AudioChunk, Tag("input_audio")],
    ],
    Discriminator(lambda m: get_discriminator(m, "type", "type")),
]
