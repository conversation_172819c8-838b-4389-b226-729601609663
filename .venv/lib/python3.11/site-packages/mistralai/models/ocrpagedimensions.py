"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing_extensions import TypedDict


class OCRPageDimensionsTypedDict(TypedDict):
    dpi: int
    r"""Dots per inch of the page-image"""
    height: int
    r"""Height of the image in pixels"""
    width: int
    r"""Width of the image in pixels"""


class OCRPageDimensions(BaseModel):
    dpi: int
    r"""Dots per inch of the page-image"""

    height: int
    r"""Height of the image in pixels"""

    width: int
    r"""Width of the image in pixels"""
