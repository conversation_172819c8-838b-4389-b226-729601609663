"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .builtinconnectors import BuiltInConnectors
from datetime import datetime
from mistralai.types import BaseModel
from typing import Any, Dict, Literal, Optional
from typing_extensions import NotRequired, TypedDict


ToolExecutionDoneEventType = Literal["tool.execution.done"]


class ToolExecutionDoneEventTypedDict(TypedDict):
    id: str
    name: BuiltInConnectors
    type: NotRequired[ToolExecutionDoneEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]
    info: NotRequired[Dict[str, Any]]


class ToolExecutionDoneEvent(BaseModel):
    id: str

    name: BuiltInConnectors

    type: Optional[ToolExecutionDoneEventType] = "tool.execution.done"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0

    info: Optional[Dict[str, Any]] = None
