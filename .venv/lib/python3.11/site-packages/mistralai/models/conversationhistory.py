"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .agenthandoffentry import Agent<PERSON>andoffEntry, AgentHandoffEntryTypedDict
from .functioncallentry import FunctionCallEntry, FunctionCallEntryTypedDict
from .functionresultentry import FunctionR<PERSON>ultEntry, FunctionResultEntryTypedDict
from .messageinputentry import MessageInputEntry, MessageInputEntryTypedDict
from .messageoutputentry import MessageOutputEntry, MessageOutputEntryTypedDict
from .toolexecutionentry import ToolExecutionEntry, ToolExecutionEntryTypedDict
from mistralai.types import BaseModel
from typing import List, Literal, Optional, Union
from typing_extensions import NotRequired, TypeAliasType, TypedDict


ConversationHistoryObject = Literal["conversation.history"]

EntriesTypedDict = TypeAliasType(
    "EntriesTypedDict",
    Union[
        FunctionResultEntryTypedDict,
        MessageInputEntryTypedDict,
        FunctionCallEntryTypedDict,
        ToolExecutionEntryTypedDict,
        MessageOutputEntryTypedDict,
        AgentHandoffEntryTypedDict,
    ],
)


Entries = TypeAliasType(
    "Entries",
    Union[
        FunctionResultEntry,
        MessageInputEntry,
        FunctionCallEntry,
        ToolExecutionEntry,
        MessageOutputEntry,
        AgentHandoffEntry,
    ],
)


class ConversationHistoryTypedDict(TypedDict):
    r"""Retrieve all entries in a conversation."""

    conversation_id: str
    entries: List[EntriesTypedDict]
    object: NotRequired[ConversationHistoryObject]


class ConversationHistory(BaseModel):
    r"""Retrieve all entries in a conversation."""

    conversation_id: str

    entries: List[Entries]

    object: Optional[ConversationHistoryObject] = "conversation.history"
