"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


AgentHandoffDoneEventType = Literal["agent.handoff.done"]


class AgentHandoffDoneEventTypedDict(TypedDict):
    id: str
    next_agent_id: str
    next_agent_name: str
    type: NotRequired[AgentHandoffDoneEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]


class AgentHandoffDoneEvent(BaseModel):
    id: str

    next_agent_id: str

    next_agent_name: str

    type: Optional[AgentHandoffDoneEventType] = "agent.handoff.done"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0
