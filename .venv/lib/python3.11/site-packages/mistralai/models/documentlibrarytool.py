"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel
from typing import List, Literal, Optional
from typing_extensions import NotRequired, TypedDict


DocumentLibraryToolType = Literal["document_library"]


class DocumentLibraryToolTypedDict(TypedDict):
    library_ids: List[str]
    r"""Ids of the library in which to search."""
    type: NotRequired[DocumentLibraryToolType]


class DocumentLibraryTool(BaseModel):
    library_ids: List[str]
    r"""Ids of the library in which to search."""

    type: Optional[DocumentLibraryToolType] = "document_library"
