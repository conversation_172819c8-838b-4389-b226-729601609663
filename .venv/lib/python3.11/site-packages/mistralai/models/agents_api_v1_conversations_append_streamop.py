"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .conversationappendstreamrequest import (
    ConversationAppendStreamRequest,
    ConversationAppendStreamRequestTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1ConversationsAppendStreamRequestTypedDict(TypedDict):
    conversation_id: str
    r"""ID of the conversation to which we append entries."""
    conversation_append_stream_request: ConversationAppendStreamRequestTypedDict


class AgentsAPIV1ConversationsAppendStreamRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""ID of the conversation to which we append entries."""

    conversation_append_stream_request: Annotated[
        ConversationAppendStreamRequest,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
