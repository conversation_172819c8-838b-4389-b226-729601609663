"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from datetime import datetime
from mistralai.types import BaseModel
from typing import Literal, Optional
from typing_extensions import NotRequired, TypedDict


AgentHandoffStartedEventType = Literal["agent.handoff.started"]


class AgentHandoffStartedEventTypedDict(TypedDict):
    id: str
    previous_agent_id: str
    previous_agent_name: str
    type: NotRequired[AgentHandoffStartedEventType]
    created_at: NotRequired[datetime]
    output_index: NotRequired[int]


class AgentHandoffStartedEvent(BaseModel):
    id: str

    previous_agent_id: str

    previous_agent_name: str

    type: Optional[AgentHandoffStartedEventType] = "agent.handoff.started"

    created_at: Optional[datetime] = None

    output_index: Optional[int] = 0
