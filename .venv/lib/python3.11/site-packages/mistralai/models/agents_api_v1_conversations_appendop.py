"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .conversationappendrequest import (
    ConversationAppendRequest,
    ConversationAppendRequestTypedDict,
)
from mistralai.types import BaseModel
from mistralai.utils import FieldMetadata, PathParamMetadata, RequestMetadata
from typing_extensions import Annotated, TypedDict


class AgentsAPIV1ConversationsAppendRequestTypedDict(TypedDict):
    conversation_id: str
    r"""ID of the conversation to which we append entries."""
    conversation_append_request: ConversationAppendRequestTypedDict


class AgentsAPIV1ConversationsAppendRequest(BaseModel):
    conversation_id: Annotated[
        str, FieldMetadata(path=PathParamMetadata(style="simple", explode=False))
    ]
    r"""ID of the conversation to which we append entries."""

    conversation_append_request: Annotated[
        ConversationAppendRequest,
        FieldMetadata(request=RequestMetadata(media_type="application/json")),
    ]
