Metadata-Version: 2.1
Name: langchain-mistralai
Version: 0.2.11
Summary: An integration package connecting Mistral and LangChain
License: MIT
Project-URL: Source Code, https://github.com/langchain-ai/langchain/tree/master/libs/partners/mistralai
Project-URL: Release Notes, https://github.com/langchain-ai/langchain/releases?q=tag%3A%22langchain-mistralai%3D%3D0%22&expanded=true
Project-URL: repository, https://github.com/langchain-ai/langchain
Requires-Python: >=3.9
Requires-Dist: langchain-core<1.0.0,>=0.3.68
Requires-Dist: tokenizers<1,>=0.15.1
Requires-Dist: httpx<1,>=0.25.2
Requires-Dist: httpx-sse<1,>=0.3.1
Requires-Dist: pydantic<3,>=2
Description-Content-Type: text/markdown

# langchain-mistralai

This package contains the LangChain integrations for [MistralAI](https://docs.mistral.ai) through their [mistralai](https://pypi.org/project/mistralai/) SDK.

## Installation

```bash
pip install -U langchain-mistralai
```

## Chat Models

This package contains the `ChatMistralAI` class, which is the recommended way to interface with MistralAI models.

To use, install the requirements, and configure your environment.

```bash
export MISTRAL_API_KEY=your-api-key
```

Then initialize

```python
from langchain_core.messages import HumanMessage
from langchain_mistralai.chat_models import ChatMistralAI

chat = ChatMistralAI(model="mistral-small")
messages = [HumanMessage(content="say a brief hello")]
chat.invoke(messages)
```

`ChatMistralAI` also supports async and streaming functionality:

```python
# For async...
await chat.ainvoke(messages)

# For streaming...
for chunk in chat.stream(messages):
    print(chunk.content, end="", flush=True)
```

## Embeddings

With `MistralAIEmbeddings`, you can directly use the default model 'mistral-embed', or set a different one if available.

### Choose model

`embedding.model = 'mistral-embed'`

### Simple query

`res_query = embedding.embed_query("The test information")`

### Documents

`res_document = embedding.embed_documents(["test1", "another test"])`