"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .completionresponsestreamchoice import (
    CompletionResponseStreamChoice,
    CompletionResponseStreamChoiceTypedDict,
)
from .usageinfo import UsageInfo, UsageInfoTypedDict
from mistralai_gcp.types import BaseModel
from typing import List, Optional
from typing_extensions import NotRequired, TypedDict


class CompletionChunkTypedDict(TypedDict):
    id: str
    model: str
    choices: List[CompletionResponseStreamChoiceTypedDict]
    object: NotRequired[str]
    created: NotRequired[int]
    usage: NotRequired[UsageInfoTypedDict]


class CompletionChunk(BaseModel):
    id: str

    model: str

    choices: List[CompletionResponseStreamChoice]

    object: Optional[str] = None

    created: Optional[int] = None

    usage: Optional[UsageInfo] = None
