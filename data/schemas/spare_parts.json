{"type": "array", "items": {"type": "object", "properties": {"equipment_name": {"type": "string", "description": "Name of the equipment that the spare part belongs to"}, "part_name": {"type": "string", "description": "Name of the spare part for procurement"}, "part_number": {"type": "string", "description": "Part number associated with the spare part"}, "drawing_number": {"type": "string", "description": "Drawing number from assembly drawings"}, "position_number": {"type": "string", "description": "Position number from assembly drawings"}, "dimension": {"type": "string", "description": "dimension of the spare part"}, "quantity": {"type": ["string", "number"], "description": "Working quantity of the spare part"}, "units": {"type": "string", "description": "Units for the working quantity"}, "materials": {"type": "string", "description": "Materials associated with the spare part"}, "remarks": {"type": "string", "description": "Remarks associated with the spare part"}, "spare_part_title": {"type": "string", "description": "Title of the spare parts table"}, "pdf_reference": {"type": "number", "description": "PDF page number where the spare part details are found"}}, "required": ["equipment_name", "part_name", "part_number", "pdf_reference"]}}