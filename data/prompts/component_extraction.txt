✅ Marine Equipment Data Extraction – Refined Instructions
Objective:
Extract major machinery, equipment, and subsystems from marine documents with precise parent-child hierarchy for integration into Planned Maintenance System (PMS) within a Marine ERP environment.
Only extract English-language content.

🔧 Extraction Guidelines:
1.⁠ ⁠Equipment Name
*Identify all major equipment.* Explicitly capture accurate equipment name. 
*Identify motors and pumps*
*Do NOT include minor items like Valves, Pressure Gauges, Thermometers, contactors  as standalone equipment.*
*Do NOT include minor items from spare parts list as standalone equipment.*
*DO NOT extract duplicate equipment unless the model is different*
If same equipment consists of different model, mention equipment model along with name

2.⁠ ⁠Parent-Child Equipment Hierarchy
Maintain explicit parent-child relationship by naming the subcomponent with full path, Example.:
Motor - Freshwater Pump - Freshwater Generator
Ejector Pump - Brine/Air Ejector - Freshwater Generator
Chemical Dosing Pump - Feed Water Treatment - Freshwater Generator

📋 Data Fields to Extract
Field	- Description
Equipment Name	- Explicitly capture accurate component name. 
Maker Name	-Manufacturer name. Verify maker if not found (mark as N/A if unavailable)
Model	-Model/type number (separate multiple models with |). Verify model if not found (mark as N/A if unavailable)
Serial Number	-Serial or manufacturer number (mark as N/A if missing)
Particulars	-Technical specs like power, capacity, flow, pressure, voltage, dimensions (Use # to separate multiple specs)
Motor Capacity	-Motor rating in kW/HP (if applicable, else N/A)
Quantity (Qty)	- Number of identical units onboard. *Identify duplicate components with same model and sumup the Qty*
Component Type	-Categorize (e.g., Centrifugal Pump, Screw Compressor, Electric Motor)
PDF Reference	-Sequential page number(s) (start from '1' from front page)
Spare Pages	- Identify Pages listing spare parts (only map once per unique equipment)
Job Pages	- Identify Pages listing maintenance/inspection/service procedures

📌 Additional Rules:
Use Drawing List, Technical Specs, Assembly Drawings, Maintenance Sections to cross-verify.
Avoid duplicate page mappings under both parent and child entries.
Maintain logical data order and validate no page reference exceeds document length.
Separate multiple models/serials with |.
Clean special characters (e.g., replace \u00d7 with '×' or appropriate symbol).
*Identify similar components with same model and sumup the Qty*

*Exceptions Rules:*
*For Compressor Machinery: Do not extract internal parts such as piston, cylinder head, valves as standalone subcomponent*
*Exclude non-critical items like fasteners or minor instruments. *