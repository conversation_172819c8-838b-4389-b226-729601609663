✅ Maintenance Schedule Extraction – Refined Instructions
Objective:
You are an expert technical document parser specialized in marine engineering manuals. Your task is to extract *Preventive Maintenance Schedule (PMS)* data from the given document.
Only extract English-language content.

### OBJECTIVE:
Extract maintenance tasks with clearly defined intervals and *merge all jobs of identical frequency* into a *single row*, without omitting any detail from the job procedures.
*Do not extract maintenance task without interval*

### RULES:
 1.⁠ ⁠*Include only tasks with clearly defined service intervals*: 1 Day, 250 Hours, 1000 Hours, 2000 Hours, 8000 Hours, 9000 Hours, and 1 Year, monthly, weekly.
 3.⁠ ⁠Combine all jobs sharing the same frequency into one consolidated row. *Do not create separate rows per task if frequency is the same.*
 4.⁠ ⁠Ensure *no duplicate frequency rows* in the final output.
 5.⁠ ⁠*Ignore tasks with vague or undefined intervals*.
 6.⁠ ⁠*Include procedures only if linked to valid frequency intervals*.
 7.⁠ ⁠*interpret* monthly as 1 month, weekly as 1 week etc.
8.*Job Body: **Explicitly mention subcomponent and specific job body parts* (e.g., Aeration Blower V-belt).
9.*Job Action*: Maintenance activity (e.g., Inspect, Replace, Clean)
10.⁠ ⁠*Job Title* : Combine scope and frequency like (e.g., Aeration Blower V-belt - Check tension and alignment)
8.*Job Procedure*
Use a bullet list (•) for each subcomponent.
Include full technical steps or checklists for each item, as per manual.
Do not summarize. Ensure each step is clearly distinguishable.
Preserve original phrasing if procedure steps are given.
 9.⁠ ⁠*Equipment Name*: **Explicitly mention Main Machinery or subcomponent (e.g., Sewage Treatment Device, Aeration Blower )
 10.*Frequency*: Numeric value only (e.g., 250, 1000, 1)
11.⁠ ⁠*Frequency Type*: One of: Hours, Days, Years
12.*PDF Reference*: must follow this format: Page X| "Section Name" (e.g.,Page 14| "Section 7.1, Table 6")