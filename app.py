#!/usr/bin/env python3
"""
Hugging Face Spaces entry point for Marine Equipment Data Extraction System.

This is the main entry point that Hugging Face Spaces will use to launch the application.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up environment variables for HF Spaces
def setup_hf_environment():
    """Set up environment variables for Hugging Face Spaces."""
    
    # Set default values if not provided
    if not os.getenv("OPENAI_API_KEY"):
        # In HF Spaces, this should be set as a secret
        print("⚠️ OPENAI_API_KEY not found. Please set it in HF Spaces secrets.")
    
    if not os.getenv("SUPABASE_URL"):
        # Set default Supabase configuration - this should be set as a secret in HF Spaces
        os.environ["SUPABASE_URL"] = "https://uscmgehdwxnupiercoqn.supabase.co"
        print("⚠️ Using default SUPABASE_URL. For production, set as HF Spaces secret.")

    if not os.getenv("SUPABASE_KEY"):
        # Set default Supabase key - this should be set as a secret in HF Spaces
        os.environ["SUPABASE_KEY"] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InVzY21nZWhkd3hudXBpZXJjb3FuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NTAyNzksImV4cCI6MjA2NjMyNjI3OX0.vSRermnPxUSpexmrL_zoUYgAKkS7upKy09MzluM9fzc"
        print("⚠️ Using default SUPABASE_KEY. For production, set as HF Spaces secret.")
    
    # Set other default configurations
    os.environ.setdefault("EMBEDDING_MODEL", "text-embedding-3-small")
    os.environ.setdefault("LLM_MODEL", "gpt-4o-mini")
    os.environ.setdefault("CHUNK_SIZE", "1000")
    os.environ.setdefault("CHUNK_OVERLAP", "200")
    os.environ.setdefault("MAX_FILE_SIZE_MB", "50")
    os.environ.setdefault("DEBUG", "false")
    os.environ.setdefault("LOG_LEVEL", "INFO")

def main():
    """Main entry point for HF Spaces."""
    print("🚀 Starting Marine Equipment Data Extraction System on Hugging Face Spaces...")
    
    # Setup environment
    setup_hf_environment()
    
    try:
        # Import and launch the application
        from ui.app import create_app
        
        # Create the Gradio interface
        app = create_app()
        
        # Simple authentication function
        def authenticate(username, password):
            valid_users = {
                "admin": "marine2024",
                "user": "extract123",
                "demo": "demo123"
            }
            return valid_users.get(username) == password

        # Launch with HF Spaces configuration
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,  # HF Spaces handles sharing
            show_error=True,
            show_api=False,
            quiet=False,
            auth=authenticate
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Trying alternative import path...")
        
        try:
            # Alternative import path
            from src.ui.app import create_app
            
            # Simple authentication function
            def authenticate(username, password):
                valid_users = {
                    "admin": "marine2024",
                    "user": "extract123",
                    "demo": "demo123"
                }
                return valid_users.get(username) == password

            app = create_app()
            app.launch(
                server_name="0.0.0.0",
                server_port=8000,
                share=False,
                show_error=True,
                show_api=False,
                quiet=False,
                auth=authenticate
            )
            
        except Exception as e2:
            print(f"❌ Failed to launch application: {e2}")
            # Create a simple error interface
            import gradio as gr
            
            def error_interface():
                with gr.Blocks(title="Marine Data Builder - Error") as interface:
                    gr.Markdown("# ❌ Application Error")
                    gr.Markdown(f"""
                    The application failed to start due to configuration issues.
                    
                    **Error Details:**
                    ```
                    {str(e2)}
                    ```
                    
                    **Common Solutions:**
                    1. Ensure OPENAI_API_KEY is set in HF Spaces secrets
                    2. Check that all required dependencies are installed
                    3. Verify database connection settings
                    
                    **Contact:** Please report this issue to the space maintainer.
                    """)
                return interface
            
            error_app = error_interface()
            error_app.launch(
                server_name="0.0.0.0",
                server_port=8000,
                share=False,
                show_error=True
            )
    
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
